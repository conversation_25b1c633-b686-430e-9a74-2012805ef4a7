import React, { useState, useEffect } from 'react';
import { Table, Spin, Alert, Button, Tooltip } from 'antd';
import Widget from '../../../../Widget';
import http_utils from '../../../../../util/http_utils';
import { MdOutlineVerifiedUser } from 'react-icons/md';
import {
    CheckCircleFilled,
    CiCircleFilled,
    NumberOutlined,
    PercentageOutlined,
    UserOutlined,
    UserSwitchOutlined,
} from '@ant-design/icons';
import ExporterModal from './ExporterModal';
import { DownloadOutlined } from '@ant-design/icons';

// API endpoint
const protoUrl = '/ace-capacity-dashboard/orders-without-hub';

const OrdersWithoutHub = ({ verticalId, parentViewData }) => {
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [viewData, setViewData] = useState(undefined);
    const [error, setError] = useState('');
    const [showExporter, setShowExporter] = useState(false);

    useEffect(() => {
        initViewData();
    }, [verticalId]);

    const initViewData = () => {
        if (isLoadingViewData) return;

        setIsLoadingViewData(true);
        setViewData(undefined);
        setError(undefined);
        let params = {
            vertical_id: verticalId,
        };
        const onComplete = (resp) => {
            setIsLoadingViewData(false);
            setViewData(resp.data);
        };
        const onError = (error) => {
            setIsLoadingViewData(false);
            setError(http_utils.decodeErrorToMessage(error));
        };
        http_utils.performGetCall(protoUrl, params, onComplete, onError);
    };

    // Define table columns
    const getColumns = () => {
        if (!viewData) return [];
        let returnData = [
            {
                title: 'State',
                dataIndex: 'state',
                key: 'state',
                sorter: (a, b) => a.state.localeCompare(b.state),
            },
            {
                title: 'City',
                dataIndex: 'city',
                key: 'city',
                sorter: (a, b) => a.city.localeCompare(b.city),
            },

            {
                title: 'Count',
                dataIndex: 'count',
                key: 'count',
                sorter: (a, b) => a.count - b.count,
            },
        ];
        return returnData;
    };

    const onExportClick = () => {
        console.log('Export button clicked');
        setShowExporter(true);
    };

    return (
        <Widget
            title="Orders Without Hub Dashboard"
            styleName="gx-card-table"
            extra={
                <Button type="link" onClick={onExportClick}>
                    <DownloadOutlined /> Export
                </Button>
            }
        >
            {isLoadingViewData && (
                <div className="gx-text-center">
                    <Spin size="large" />
                    <p>Loading orders without hub data...</p>
                </div>
            )}
            {!isLoadingViewData && error && (
                <div>
                    <p className="gx-text-red">{error}</p>
                    <Button
                        type="secondary"
                        className=" gx-my-2"
                        onClick={() => initViewData()}
                    >
                        Retry
                    </Button>
                </div>
            )}

            {viewData && (
                <div className="gx-table-responsive">
                    <Table
                        columns={getColumns()}
                        dataSource={viewData.orders_without_hub}
                        pagination={{
                            pageSize: 10,
                            showSizeChanger: true,
                            pageSizeOptions: ['10', '20', '50', '100'],
                        }}
                        bordered
                        rowClassName={() => 'bold-row'}
                    />
                </div>
            )}
            <ExporterModal
                showEditor={showExporter}
                onClose={() => {
                    console.log('Closing exporter modal');
                    setShowExporter(false);
                }}
                verticalId={verticalId}
            />
        </Widget>
    );
};

export default OrdersWithoutHub;
