import React, { Component } from 'react';
import { Button, message, Modal } from 'antd';
import CSVExporter from '../../../../../components/wify-utils/CSVExporter';
import ConfigHelpers from '../../../../../util/ConfigHelpers';
import { getCurrentDateAndTimeFrDisplay } from '../../../../../util/helpers';

// Base URL for the export API
const getSubmitUrl = (verticalId) =>
    `/ace-capacity-dashboard/export-orders-without-hubs?vertical_id=${verticalId}`;

class ExporterModal extends Component {
    constructor(props) {
        super(props);
        this.state = {
            render_helper: false,
            visible: props.showEditor || false,
            isFormSubmitting: false,
            viewData: undefined,
            isLoadingViewData: false,
            error: '',
        };
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.showEditor !== this.props.showEditor) {
            this.setState({
                visible: this.props.showEditor,
            });
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false,
        });
        this.updateClosureToParent();
    };

    updateClosureToParent = () => {
        if (this.props.onClose) {
            this.props.onClose();
        }
    };

    getMetaFrExporter = () => {
        return {
            title: 'Export Orders Without Hubs',
            email_id: `${ConfigHelpers.getUserEmailId()}`,
            subject: `WIFY ACE Orders Without Hubs dump ${getCurrentDateAndTimeFrDisplay()}`,
            filters: {},
            section_wise_meta: [],
            filename: `Orders_Without_Hub_${getCurrentDateAndTimeFrDisplay()}`,
            // vertical_id is now passed in the URL
            filters: {}, // Empty filters object as we're not filtering the data
        };
    };

    render() {
        const { isFormSubmitting, visible } = this.state;
        return visible ? (
            <Modal
                visible={visible}
                confirmLoading={isFormSubmitting}
                width={700}
                footer={null}
                onCancel={this.handleCancel}
                title="Export Orders Without Hubs"
            >
                <CSVExporter
                    data-testid="csv-exporter"
                    exportMeta={this.getMetaFrExporter()}
                    submitUrl={getSubmitUrl(this.props.verticalId)}
                    onSubmitComplete={(resp) => {
                        message.success(
                            'Email will be sent shortly, Export data request added successfully'
                        );
                        this.handleCancel();
                    }}
                    isFiltersAppliedHide={true}
                    noColumnSelection={true}
                />
            </Modal>
        ) : (
            <></>
        );
    }
}

export default ExporterModal;
