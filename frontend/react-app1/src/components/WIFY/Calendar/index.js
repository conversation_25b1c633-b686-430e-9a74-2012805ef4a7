import React from 'react';
import { Button, Typography } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import moment from 'moment';
import './index.css';

const { Text, Title } = Typography;

const CalendarGrid = ({
    currentDate,
    selectedDate,
    availabilityData = {},
    onNavigate,
    onDateSelect,
    onDotColor,
}) => {
    const today = moment();

    const generateCalendarDays = () => {
        const startOfMonth = currentDate.clone().startOf('month');
        const endOfMonth = currentDate.clone().endOf('month');
        const startOfWeek = startOfMonth.clone().startOf('week');
        const endOfWeek = endOfMonth.clone().endOf('week');

        const days = [];
        let day = startOfWeek.clone();

        while (day.isSameOrBefore(endOfWeek, 'day')) {
            days.push(day.clone());
            day.add(1, 'day');
        }

        return days;
    };

    const calendarDays = generateCalendarDays();

    return (
        <>
            {/* Calendar Header */}
            <div className="gx-d-flex gx-justify-content-between gx-align-items-center gx-p-2">
                <Button
                    icon={<LeftOutlined className="gx-fs-sm gx-mb-0" />}
                    onClick={() => onNavigate(-1)}
                    type="text"
                    className="gx-mb-0"
                    size="small"
                />
                <Title level={5} className="gx-mb-0 gx-text-black">
                    {currentDate.format('MMMM YYYY')}
                </Title>
                <Button
                    icon={<RightOutlined className="gx-fs-sm gx-mb-0" />}
                    onClick={() => onNavigate(1)}
                    type="text"
                    className="gx-mb-0"
                    size="small"
                />
            </div>

            {/* Days of week header */}
            <div className="calendar-grid gx-mb-1">
                {['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'].map(
                    (day) => (
                        <div
                            key={day}
                            className="gx-flex-1 gx-text-center"
                            style={{ fontSize: '12px' }}
                        >
                            {day}
                        </div>
                    )
                )}
            </div>

            {/* Calendar Grid */}
            <div className="calendar-grid-wrapper">
                {Array.from(
                    { length: Math.ceil(calendarDays.length / 7) },
                    (_, weekIndex) => (
                        <div key={weekIndex} className="calendar-grid">
                            {calendarDays
                                .slice(weekIndex * 7, (weekIndex + 1) * 7)
                                .map((day, dayIndex) => {
                                    const isCurrentMonth = day.isSame(
                                        currentDate,
                                        'month'
                                    );
                                    const isToday = day.isSame(today, 'day');
                                    const isSelected = day.isSame(
                                        selectedDate,
                                        'day'
                                    );
                                    const dotColor = onDotColor(day);

                                    return (
                                        <div
                                            key={dayIndex}
                                            className={`calendar-day gx-flex-1 gx-text-center gx-cursor-pointer gx-position-relative  gx-d-flex gx-flex-column gx-align-items-center gx-justify-content-center gx-rounded-circle gx-mx-auto calendar-day-fixed ${
                                                isCurrentMonth
                                                    ? ''
                                                    : 'gx-text-grey'
                                            } ${isToday ? 'today' : ''} ${isSelected ? 'selected' : ''}`}
                                            onClick={() => onDateSelect(day)}
                                        >
                                            <Text
                                                className={
                                                    day.month() ===
                                                    currentDate.month()
                                                        ? 'gx-text-black'
                                                        : 'gx-text-grey'
                                                }
                                            >
                                                {day.date()}
                                            </Text>

                                            {/* Availability dot */}
                                            {isCurrentMonth && (
                                                <span
                                                    className={`wy-calendar-day-${dotColor} gx-rounded-circle gx-mt-1 `}
                                                    style={{
                                                        width: '5px',
                                                        height: '5px',
                                                        opacity:
                                                            availabilityData[
                                                                day.format(
                                                                    'YYYY-MM-DD'
                                                                )
                                                            ]
                                                                ? 1
                                                                : 0.3,
                                                    }}
                                                />
                                            )}
                                        </div>
                                    );
                                })}
                        </div>
                    )
                )}
            </div>
        </>
    );
};

export default CalendarGrid;
