import { List, Pagination, Skeleton, Spin, Table } from 'antd';
import React, { Component } from 'react';
import http_utils from '../../../../util/http_utils';

const getRandomuserParams = (params) => ({
    results: params.pagination.pageSize,
    page: params.pagination.page,
    ...params,
});

class ListviewWithPaginationV2 extends Component {
    state = {
        data: [],
        pagination: {
            current: 1,
            pageSize:
                this.props.pageSize || (this.props.customPageSize ? 50 : 10),
        },
        loading: false,
        // forQuickAssign : true
    };

    componentDidMount() {
        const { pagination } = this.state;
        this.fetch({ pagination }, () =>
            this.fetch({ pagination, retrieve_count: true })
        );
    }

    componentDidUpdate(prevProps, prevState) {
        // check if filter changed, if yes trigger a fetch again

        const isSearchContentUpdated =
            prevProps.searchQuery !== this.props.searchQuery;

        if (
            prevProps.filterObject !== this.props.filterObject ||
            isSearchContentUpdated
        ) {
            const performFetchApiCall = () => {
                const { pagination } = this.state;
                this.fetch({ pagination }, () => {
                    this.fetch({ pagination, retrieve_count: true });
                });
            };
            if (isSearchContentUpdated) {
                const pagination = this.state.pagination;
                pagination.current = 1;
                this.setState({ pagination }, performFetchApiCall);
            } else {
                performFetchApiCall();
            }
        }
        if (prevState.loading !== this.state.loading) {
            if (this.props.onLoadingChange) {
                this.props.onLoadingChange({
                    dataLoadingStatus: this.state.loading,
                });
            }
        }
    }

    handlePaginationChange = (page, pageSize) => {
        this.setState({
            pagination: {
                current: this.state.pagination.current,
                pageSize: pageSize,
            },
        });
        this.fetch(
            {
                pagination: {
                    current: page,
                    pageSize: pageSize,
                },
            },
            () => {
                this.fetch({
                    pagination: {
                        current: page,
                        pageSize: pageSize,
                    },
                    retrieve_count: true,
                });
            }
        );
    };

    fetch = (params = {}, callBackFn) => {
        params = {
            ...params,
            filters: this.props.filterObject,
            search_query: this.props.searchQuery ? this.props.searchQuery : '',
        };
        const retrieve_count = params.retrieve_count || false;

        if (this.props.extraFilterObject) {
            params['filters'] = {
                ...params?.filters,
                ...this.props?.extraFilterObject,
            };
        }
        if (this.props.extraParams) {
            params = { ...params, ...this.props.extraParams };
        }
        if (!retrieve_count) {
            this.setState({ loading: true });
        } else {
            this.setState({ totalCountLoading: true });
        }
        const onSuccess = (data) => {
            if (this.props.onApiRespChange) {
                this.props.onApiRespChange(data.data, retrieve_count);
            }
            if (!retrieve_count) {
                this.setState(
                    {
                        loading: false,
                        data: data.data.data,
                        api_resp: data.data,
                    },
                    () => {
                        if (callBackFn) {
                            callBackFn();
                        }
                    }
                );
            } else {
                this.setState(
                    {
                        totalCountLoading: false,
                        pagination: {
                            ...this.state.pagination,
                            ...data.data.pagination,
                        },
                    },
                    () => {
                        if (this.props.onTotalUpdated) {
                            this.props.onTotalUpdated(
                                this.state.pagination?.total
                            );
                        }
                    }
                );
            }
        };
        const onError = (error) => {
            if (!retrieve_count) {
                this.setState({
                    loading: false,
                    data: [],
                });
            } else {
                this.setState({
                    totalCountLoading: false,
                });
            }
        };
        http_utils.performGetCall(
            this.props.dataSourceApi,
            getRandomuserParams(params),
            onSuccess,
            onError
        );
    };

    tableOnRowClick(singleRowData) {
        if (this.props.tableOnRowClick) {
            this.props.tableOnRowClick(singleRowData);
        }
    }

    getOverFlowScrollBarProps() {
        if (this.props.overflowScrollbar == undefined) {
            return '';
        }
        let { x, y } = this.props.overflowScrollbar;
        let returnProps = { x, y };
        if (returnProps['x'] == undefined) {
            returnProps['x'] = 1500;
        }

        return returnProps;
    }
    getTableView(data, loading) {
        if (this.props?.skeletonLoading) {
            if (!loading) {
                return (
                    <Table
                        className="wy-cursor-pointer"
                        columns={this.props.columns}
                        dataSource={data}
                        pagination={false}
                        // loading={loading}
                        onRow={(singleRowData) => ({
                            onClick: () => this.tableOnRowClick(singleRowData),
                        })}
                        scroll={this.getOverFlowScrollBarProps()}
                        bordered={this.props?.bordered || false}
                        sticky={this.props?.sticky || false}
                        size={this.props?.tableSize || 'middle'}
                        rowSelection={this.props.rowSelection || null}
                        rowClassName={(record) =>
                            record?.is_restricted_view ? 'wy-restricted-table-row' : ''
                        }
                    />
                );
            } else {
                return <></>;
            }
        } else {
            return (
                <Table
                    className="wy-cursor-pointer"
                    columns={this.props.columns}
                    dataSource={data}
                    pagination={false}
                    loading={loading}
                    onRow={(singleRowData) => ({
                        onClick: () => this.tableOnRowClick(singleRowData),
                    })}
                    scroll={this.getOverFlowScrollBarProps()}
                    bordered={this.props?.bordered || false}
                    sticky={this.props?.sticky || false}
                    size={this.props?.tableSize || 'middle'}
                    rowSelection={this.props.rowSelection || null}
                    rowClassName={(record) =>
                        record?.is_restricted_view ? 'wy-restricted-table-row' : ''
                    }
                />
            );
        }
    }

    getGridView(data, loading, api_resp) {
        if (this.props?.skeletonLoading) {
            if (!loading) {
                return (
                    <>
                        <List
                            className="gx-d-md-none"
                            // loading={loading}
                            dataSource={data}
                            grid={{ gutter: 10, column: 1 }}
                            renderItem={(item) =>
                                this.props.renderSingleItem(item, api_resp)
                            }
                        />
                        <List
                            className="gx-d-none gx-d-md-block"
                            // loading={loading}
                            dataSource={data}
                            grid={{
                                gutter: 10,
                                column: this.props.columns
                                    ? this.props.columns
                                    : 1,
                            }}
                            renderItem={(item) =>
                                this.props.renderSingleItem(item, api_resp)
                            }
                        />
                    </>
                );
            } else {
                return <></>;
            }
        } else {
            return (
                <>
                    <List
                        className="gx-d-md-none"
                        loading={loading}
                        dataSource={data}
                        grid={{ gutter: 10, column: 1 }}
                        renderItem={(item) =>
                            this.props.renderSingleItem(item, api_resp)
                        }
                    />
                    <List
                        className="gx-d-none gx-d-md-block"
                        loading={loading}
                        dataSource={data}
                        grid={{
                            gutter: 10,
                            column: this.props.columns ? this.props.columns : 1,
                        }}
                        renderItem={(item) =>
                            this.props.renderSingleItem(item, api_resp)
                        }
                    />
                </>
            );
        }
    }

    render() {
        const { data, pagination, loading, api_resp, totalCountLoading } =
            this.state;

        return (
            <div>
                {this.props.tableView
                    ? this.getTableView(data, loading)
                    : this.getGridView(data, loading, api_resp)}

                {!loading && totalCountLoading && (
                    <div className="gx-mt-2 gx-module-box-content">
                        <Spin />
                    </div>
                )}
                {/* {pagination.total == null &&
                    this.props.goToDashboardButton &&
                    !this.state.loading &&
                    this.props.noDataFooter()} */}
                {!loading && !totalCountLoading && pagination.total > 0 && (
                    <>
                        <Pagination
                            className="gx-mt-3"
                            {...pagination}
                            onChange={this.handlePaginationChange}
                            size={this.props?.paginationSize || 'default'}
                        />
                        {this.props.noTotal
                            ? null
                            : `Total - ${pagination.total}`}
                    </>
                )}
            </div>
        );
    }
}

export default ListviewWithPaginationV2;
