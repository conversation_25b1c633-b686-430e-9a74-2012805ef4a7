import React, { Component } from 'react';
import { message, Spin } from 'antd';
import http_utils from '../../util/http_utils';
import ConfigHelpers from '../../util/ConfigHelpers';
import _ from 'lodash';

const userDetailsUrl = '/user';
let userIdVsDetailsCache = {};
class UserName extends Component {
    constructor(props) {
        super(props);
    }

    state = {
        isLoadingViewData: false,
        error: '',
    };

    componentDidMount() {
        this.getDailyStatusUpdatedUserNameByApi();
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.id != this.props.id) {
            this.getDailyStatusUpdatedUserNameByApi();
        }
    }

    getDailyStatusUpdatedUserNameByApi() {
        if (userIdVsDetailsCache[this.props.id] == undefined) {
            this.setState({
                isLoadingViewData: true,
            });

            var params = { showInActive: this.props.showInActive };

            const onComplete = (resp) => {
                userIdVsDetailsCache[this.props.id] = resp.data;
                this.setState({
                    isLoadingViewData: false,
                });
            };

            const onError = (error) => {
                message.error('Unable to load');
                this.setState({
                    error: http_utils.decodeErrorToMessage(error),
                    isLoadingViewData: false,
                });
            };
            let url = userDetailsUrl + '/' + this.props.id;
            http_utils.performGetCall(url, params, onComplete, onError);
        }
    }

    getDailyStatusUpdatedUserName() {
        // debugger
        if (this.props.prefix && this.props.postfix) {
            return (
                this.props.prefix +
                ' ' +
                userIdVsDetailsCache[this.props.id]?.name +
                ' ' +
                this.props.postfix
            );
        } else if (this.props.prefix) {
            return (
                this.props.prefix +
                ' ' +
                userIdVsDetailsCache[this.props.id]?.name
            );
        } else if (this.props.postfix) {
            return (
                userIdVsDetailsCache[this.props.id]?.name +
                ' ' +
                this.props.postfix
            );
        } else {
            return userIdVsDetailsCache[this.props.id]?.name;
        }
    }

    render() {
        const { isLoadingViewData, error } = this.state;

        return (
            <span>
                {isLoadingViewData ? (
                    <Spin className="gx-mb-0 gx-ml-2" />
                ) : userIdVsDetailsCache[this.props.id] == undefined ? (
                    <span>{this.props.customError || error}</span>
                ) : (
                    this.getDailyStatusUpdatedUserName()
                )}
            </span>
        );
    }
}

export default UserName;
