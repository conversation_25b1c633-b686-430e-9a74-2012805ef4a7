import { UserOutlined } from '@ant-design/icons';
import React from 'react';
import { FaUserAstronaut } from 'react-icons/fa';
import CountUp from '../../components/wify-utils/CountUp/countUp';

const AutoAssignWidget = ({
    readOnly = false,
    assigneeCount,
    requestPending,
    onAutoAssignClick,
    enableAutoAssignBtn,
}) => {
    return (
        <div className="gx-align-items-center wy-flex-responsive">
            <div className="h3">
                <span className="">
                    <UserOutlined />
                    <CountUp start={0} end={assigneeCount} duration={1} />
                </span>
                <span className="gx-m-1">
                    <i className="icon icon-arrow-right gx-mr-1 gx-vertical-align-middle" />
                </span>
                <span className="">
                    <i className="icon icon-home gx-mr-1 gx-vertical-align-middle" />
                    <CountUp start={0} end={requestPending} duration={1} />
                </span>
            </div>
            {
                <button
                    className={`${readOnly ? 'wy-auto-assign-button' : 'wy-shine-button'} gx-px-3 gx-py-2`}
                    onClick={onAutoAssignClick}
                    {...(enableAutoAssignBtn ? {} : { disabled: true })}
                >
                    <div className="gx-align-items-center wy-flex-responsive gx-flex-row">
                        <FaUserAstronaut className="gx-fs-2xl" />
                        <div>Auto Assign</div>
                    </div>
                </button>
            }
        </div>
    );
};

export default AutoAssignWidget;
