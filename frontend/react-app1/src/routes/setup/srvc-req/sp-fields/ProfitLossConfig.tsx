import React, { JSX, useEffect, useState } from 'react';
//import FormBuilder from 'antd-form-builder';
import { validateLambdaArn } from '../../../../util/helpers';
import InputTable from '../../../../components/WIFY/subtasks/InputTable';
import CircularProgress from '../../../../components/CircularProgress';
import { getUsersInfoMeta } from '../../../users/helper';
import ConfigHelpers from '../../../../util/ConfigHelpers';
import { SP_USER_EXCLUDED_FIELDS } from '../../../../util/constants';
import { decodeFieldsMetaFrmJson } from '../../../../components/wify-utils/FieldCreator/helpers';
import http_utils from '../../../../util/http_utils';
import { FormInstance, Tabs } from 'antd';

const FormBuilder = require('antd-form-builder').default;

interface ProfitLossConfigProps {
    form: React.RefObject<FormInstance>;
    roleList: { label: string; value: string }[];
    form_data: { form_data?: { revenue_column_meta?: string }; id: number };
    editMode: Boolean;
}
interface FieldOption {
    label: string;
    value: string;
    key: string;
}
interface UserField {
    key: string;
    label: string;
    cust_component?: string;
    type?: string;
    rules?: Array<{ type: string }>;
}

interface RevenueConfigProps {
    tabKey: string;
}

interface RevenueConfigItem {
    key: string;
    className?: string;
    widgetProps?: { hidden: boolean };
    render?: () => JSX.Element;
}
const profitLossUrl = '/profit-loss';

const ProfitLossConfig: React.FC<ProfitLossConfigProps> = ({
    form,
    roleList,
    form_data,
    editMode,
}) => {
    const [apiRespData, setApiRespData] = useState<any>({});
    const [renderHelper, setRenderHelper] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [isLoadingViewData, setIsLoadingViewData] = useState<boolean>(false);

    useEffect(() => {
        initViewData();
    }, []);

    const refresh = (): void => {
        setRenderHelper(!renderHelper);
    };

    const initViewData = () => {
        if (!isLoadingViewData) {
            const onComplete = (resp: any) => {
                setIsLoadingViewData(false);
                setApiRespData({ ProfitAndLossData: resp?.data });
            };
            let params = { vertical_type_id: form_data?.id };
            const onError = (error: any) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };

            http_utils.performGetCall(
                `${profitLossUrl}/config`,
                params,
                onComplete,
                onError
            );
        }
    };

    /**
     * Handles the synchronization of profit and loss (PL) value authorization roles.
     * This function ensures that only the selected roles remain in the form field
     * `roles_who_can_sync_pl_values`.
     *
     * @param {Object} params - The function parameters.
     * @param {string[]} params.selectedRoles - The list of selected roles.
     */
    const profitLossAuthorisedRolesHandler = ({
        selectedRoles,
    }: {
        selectedRoles: string[];
    }) => {
        const selectedRolesWhoCanSyncProfitLossValues =
            form?.current?.getFieldValue('roles_who_can_sync_pl_values');
        if (selectedRolesWhoCanSyncProfitLossValues) {
            const modifiedSelectedRoles =
                selectedRolesWhoCanSyncProfitLossValues?.filter(
                    (role: string) => selectedRoles.includes(role)
                );
            form?.current?.setFieldsValue({
                roles_who_can_sync_pl_values: modifiedSelectedRoles,
            });
        }
    };

    /**
     * Retrieves possible number fields from custom fields in the form.
     * This function extracts fields that have a widget type of 'number'
     * and returns them as an array of label-value pairs.
     *
     * @returns {Array<Object>} An array of objects, each containing:
     *  - `label` {string}: The label of the number field.
     *  - `value` {string}: The key of the number field.
     *  - `key` {string}: The key of the number field.
     */
    const getPossibleNumberFields = (): FieldOption[] => {
        const customFields = form?.current?.getFieldValue(
            'sp_cust_fields_json'
        );
        const translatedFields = decodeFieldsMetaFrmJson(customFields);
        return (
            translatedFields
                ?.filter((field: any) => field.widget === 'number')
                ?.map((field: any) => ({
                    label: field.label,
                    value: field.key,
                    key: field.key,
                })) || []
        );
    };

    /**
     * Retrieves specific custom fields from the form.
     * This function extracts fields based on specific widget types and conditions,
     * filtering out unsupported custom components and widgets.
     *
     * @returns {Array<Object>} An array of objects, each containing:
     *  - `label` {string}: The label of the custom field.
     *  - `value` {string}: The key of the custom field.
     *  - `key` {string}: The key of the custom field.
     */
    const getSpSpecificCustomFields = (): FieldOption[] => {
        const customFields = form?.current?.getFieldValue(
            'sp_cust_fields_json'
        );
        const translatedFields = decodeFieldsMetaFrmJson(
            customFields,
            undefined,
            false,
            false
        );
        return (
            translatedFields
                ?.filter(
                    (field: any) =>
                        (field.widget === 'select' &&
                            (!field.widgetProps ||
                                field.widgetProps.mode !== 'multiple')) ||
                        field.widget === 'textarea' ||
                        (!field.widget &&
                            field.label &&
                            !field.cust_component &&
                            !field.cust_widget &&
                            field.type !== 'Barcode_scanner' &&
                            field.type !== 'WIFY_BLE_COMPONENT' &&
                            field.rules?.[0]?.type !== 'email')
                )
                .map((field: any) => ({
                    label: field.label,
                    value: field.key,
                    key: field.key,
                })) || []
        );
    };

    /**
     * Filters options based on already selected values in revenue column.
     * This function ensures that options already chosen in the given row
     * and in the revenue column metadata are excluded from the available options.
     *
     * @param {Object} row - The data row containing selected values.
     * @param {string[]} keysToCheck - The keys to check in the row and metadata.
     * @param {Array<Object>} optionsSource - The list of available options.
     *
     * @returns {Array<Object>} A filtered list of options excluding already selected values.
     */
    const getFilteredOptions = (
        row: Record<string, any>,
        keysToCheck: string[],
        optionsSource: { value: string }[]
    ): { value: string }[] => {
        let alreadySelectedValue: Record<string, any>[] = [];

        if (form_data.form_data?.revenue_column_meta) {
            try {
                alreadySelectedValue =
                    JSON.parse(form_data.form_data?.revenue_column_meta) || [];
            } catch (error) {
                console.error('Error parsing revenue_column_meta:', error);
            }
        }

        let selectedValues: string[] = [];

        keysToCheck.forEach((key) => {
            if (row[key]) {
                selectedValues.push(row[key]);
            }
            alreadySelectedValue.forEach((item) => {
                if (item[key] === row[key]) {
                    selectedValues.push(item[key]);
                }
            });
        });

        return optionsSource.filter(
            (option) => !selectedValues.includes(option.value)
        );
    };

    /**
     * Generates the configuration metadata for the Profit & Loss (P&L) settings form.
     *
     * @returns {Object} An object containing form field configurations for P&L settings.
     */
    const getRowDataFrRevenue = () => {
        try {
            const fieldValue = form.current?.getFieldValue(
                'revenue_column_meta'
            );
            return fieldValue ? JSON.parse(fieldValue) || [] : [{}];
        } catch (error) {
            console.log(
                `ProfitLossConfig :: getRowDataFrRevenue :: error ::`,
                error
            );
            return {};
        }
    };

    const getColMetaFrRevenue = () => {
        const customFieldOptions = getSpSpecificCustomFields();
        const possibleNumberFields = getPossibleNumberFields();

        let colMeta = [
            {
                key: 'sku',
                label: 'SKU',
                widget: 'select',
                disable_filter: true,
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                dynamicMeta: (
                    row: Record<string, unknown>,
                    original_field_meta: Record<string, unknown>
                ) => {
                    let dynamicMetaRow = { ...original_field_meta };
                    dynamicMetaRow['options'] = customFieldOptions;

                    return dynamicMetaRow;
                },
            },
            {
                key: 'item_name',
                label: 'Item Name',
                widget: 'select',
                disable_filter: true,
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                dynamicMeta: (
                    row: Record<string, unknown>,
                    original_field_meta: Record<string, unknown>
                ) => {
                    let dynamicMetaRow = { ...original_field_meta };
                    dynamicMetaRow['options'] = customFieldOptions;

                    return dynamicMetaRow;
                },
            },
            {
                key: 'qty',
                label: 'Quantity',
                widget: 'select',
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                disable_filter: true,
                dynamicMeta: (
                    row: Record<string, unknown>,
                    original_field_meta: Record<string, unknown>
                ) => {
                    let dynamicMetaRow = { ...original_field_meta };
                    dynamicMetaRow['options'] = possibleNumberFields;

                    return dynamicMetaRow;
                },
            },
        ];
        return colMeta;
    };

    /**
     * Retrieves user fields for a specific resource type while excluding predefined fields.
     *
     * @returns {Array<{ value: string, label: string }>} An array of user fields with `value` as the key and `label` as the display name.
     */
    const userFieldFrResourceType = (): { value: string; label: string }[] => {
        const isServiceProvider = ConfigHelpers.isServiceProvider();
        const userConfigData =
            apiRespData?.ProfitAndLossData?.user_custom_fields?.[0] || {};

        const userFields = getUsersInfoMeta(
            userConfigData,
            {},
            false,
            false,
            refresh,
            {},
            isServiceProvider,
            form,
            {},
            editMode
        )?.fields;

        if (!userFields) return [];

        const excludeKeys = new Set(Object.values(SP_USER_EXCLUDED_FIELDS));
        const excludedCustComponents = new Set([
            'linebreak',
            'legend',
            'Mobile',
            'WIFY_CAMERA',
            'WIFY_MIC',
            'Files',
        ]);
        const excludedCompTypes = new Set([
            'WIFY_BLE_COMPONENT',
            'Barcode_scanner',
        ]);

        return userFields
            .filter(
                ({ key, cust_component, type, rules }) =>
                    !excludeKeys.has(key) &&
                    (!cust_component ||
                        !excludedCustComponents.has(cust_component)) &&
                    (!type || !excludedCompTypes.has(type)) &&
                    !rules?.some((rule: any) => rule.type === 'email')
            )
            .map(({ key, label }: UserField) => ({ value: key, label }));
    };

    /**
     * Extracts service type-specific custom fields that match certain widget types.
     * @param {Array} customFields - The custom fields data.
     * @returns {Array<{label: string, value: string, key: string}>} - An array of label-value-key pairs.
     */
    const getSrvcTypeSpecificCustomFields = (customFields) => {
        const translatedFields = decodeFieldsMetaFrmJson(
            customFields,
            undefined,
            false,
            false
        );

        let labelValuePairData = [];
        if (translatedFields) {
            translatedFields.forEach((singleField) => {
                if (
                    (singleField?.widget == 'select' &&
                        (!singleField.widgetProps ||
                            singleField?.widgetProps?.mode !== 'multiple')) ||
                    singleField?.widget == 'textarea' ||
                    (!singleField?.widget &&
                        singleField?.label &&
                        !singleField?.cust_component &&
                        !singleField?.cust_widget &&
                        singleField?.type != 'Barcode_scanner' &&
                        singleField?.type != 'WIFY_BLE_COMPONENT' &&
                        singleField.rules?.[0]?.type != 'email')
                ) {
                    labelValuePairData.push({
                        label: singleField.label,
                        value: singleField.key,
                        key: singleField.key,
                    });
                }
            });
        }

        return labelValuePairData;
    };

    /**
     * Retrieves possible number fields from the given custom fields.
     * @param {Array} customFields - The custom fields data.
     * @returns {Array<{label: string, value: string, key: string}>} - An array of number fields.
     */
    const getPossibleSrvcTypeNumberFields = (customFields) => {
        const translatedFields = decodeFieldsMetaFrmJson(customFields);

        let labelValuePairData = [];
        if (translatedFields) {
            translatedFields.forEach((singleField) => {
                if (singleField?.widget == 'number') {
                    labelValuePairData.push({
                        label: singleField.label,
                        value: singleField.key,
                        key: singleField.key,
                    });
                }
            });
        }

        return labelValuePairData;
    };

    /**
     * Retrieves row data for the service type revenue configuration table.
     *
     * This function extracts existing rules from the form field
     * `srvc_type_revenue_column_meta` and maps them to possible service types
     * from the API response (`apiRespData.ProfitAndLossData.possible_srvc_types`).
     * It ensures that each service type and organization combination has a unique row.
     *
     * @returns {Array<Object>} An array of row data objects, where each object contains:
     *  - `row_id` (string): Unique identifier for the row based on service type and organization.
     *  - `srvc_type` (string): The service type ID.
     *  - `srvc_req_status` (string | undefined): The existing service request status, if available.
     */
    const getRowDataFrSrvcTypeRevenue = () => {
        let rulesJson = form?.current?.getFieldValue(
            'srvc_type_revenue_column_meta'
        );

        // rulesjson are the existing rules previously set by user
        let existingRules: any[] = [];
        if (rulesJson) {
            existingRules = JSON.parse(rulesJson);
        }

        let possible_srvc_types =
            apiRespData?.ProfitAndLossData?.possible_srvc_types;

        let rowData: any[] = [];

        if (possible_srvc_types) {
            possible_srvc_types.map((singleOrgData) => {
                //make row_data for table
                const row_id =
                    singleOrgData.service_type_id + '_' + singleOrgData.org_id;
                const rowDataObj = {
                    row_id: row_id,
                    srvc_type: singleOrgData.service_type_id,
                };

                if (existingRules) {
                    const existingRule = existingRules.filter(
                        (singleRule) => singleRule.row_id == row_id
                    )[0];

                    if (existingRule != undefined) {
                        // console.log('existingRule',existingRule);
                        rowDataObj['sku'] = existingRule['sku'];
                        rowDataObj['item_name'] = existingRule['item_name'];
                        rowDataObj['qty'] = existingRule['qty'];
                    }
                }
                rowData.push(rowDataObj);
            });
        }

        // console.log('rowData-', rowData);
        return rowData;
    };

    /**
     * Generates column metadata for the service type revenue configuration table.
     *
     * This function retrieves possible service types from the API response and
     * constructs column metadata with dynamic dropdown options based on
     * service type-specific fields.
     *
     * @returns {Array<Object>} An array of column metadata objects, where each object contains:
     *  - `key` (string): The unique key for the column.
     *  - `label` (string): The display label of the column.
     *  - `widget` (string): The type of widget used in the column (e.g., 'select').
     *  - `widgetProps` (Object): Additional properties for the widget.
     *  - `options` (Array<Object>): Available options for dropdown-type columns.
     *  - `disable_filter` (boolean): Whether filtering is disabled for the column.
     *  - `dynamicMeta` (Function): A function that dynamically updates metadata
     *    based on the selected service type.
     */
    const getColMetaFrSrvcTypeRevenue = () => {
        //get orgs_data srvc_type_id wise
        let possible_srvc_types =
            apiRespData?.ProfitAndLossData?.possible_srvc_types;

        let srvc_type_id_options: any[] = [];
        let skuAndItemNameOptionsBySrvcTypeId: Record<string, any[]> = {};
        let quantityOptionsBySrvcTypeId: Record<string, any[]> = {};

        if (possible_srvc_types) {
            possible_srvc_types.map((singleOrgData) => {
                //make srvc_type option
                let srvc_type_obj = {
                    label: singleOrgData.nickname + ' - ' + singleOrgData.title,
                    value: singleOrgData.service_type_id,
                };

                const customFieldOptions = getSrvcTypeSpecificCustomFields(
                    singleOrgData?.srvc_cust_fields_json
                );
                const possibleNumberFields = getPossibleSrvcTypeNumberFields(
                    singleOrgData?.srvc_cust_fields_json
                );
                srvc_type_id_options.push(srvc_type_obj);
                skuAndItemNameOptionsBySrvcTypeId[
                    singleOrgData.service_type_id
                ] = customFieldOptions;
                quantityOptionsBySrvcTypeId[singleOrgData.service_type_id] =
                    possibleNumberFields;
            });
        }

        let colMeta = [
            {
                key: 'srvc_type',
                label: 'Service type',
                widget: 'select',
                widgetProps: { disabled: true },
                options: srvc_type_id_options,
            },
            {
                key: 'sku',
                label: 'SKU',
                widget: 'select',
                options: [],
                disable_filter: true,
                // rules: [
                //     {
                //         required: true,
                //         message: 'Please enter SKU',
                //     },
                // ],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                dynamicMeta: (row, original_field_meta) => {
                    let dynamicMetaRow = { ...original_field_meta };

                    let optionsFrCurrSrvcTypeId =
                        skuAndItemNameOptionsBySrvcTypeId[row.srvc_type];
                    if (optionsFrCurrSrvcTypeId) {
                        dynamicMetaRow['options'] = [
                            ...dynamicMetaRow['options'],
                            ...optionsFrCurrSrvcTypeId,
                        ];
                    }

                    return dynamicMetaRow;
                },
            },
            {
                key: 'item_name',
                label: 'Item Name',
                widget: 'select',
                options: [],
                disable_filter: true,
                // rules: [
                //     {
                //         required: true,
                //         message: 'Please enter Item Name',
                //     },
                // ],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                dynamicMeta: (row, original_field_meta) => {
                    let dynamicMetaRow = { ...original_field_meta };

                    let optionsFrCurrSrvcTypeId =
                        skuAndItemNameOptionsBySrvcTypeId[row.srvc_type];
                    if (optionsFrCurrSrvcTypeId) {
                        dynamicMetaRow['options'] = [
                            ...dynamicMetaRow['options'],
                            ...optionsFrCurrSrvcTypeId,
                        ];
                    }

                    return dynamicMetaRow;
                },
            },
            {
                key: 'qty',
                label: 'Quantity',
                widget: 'select',
                options: [],
                // rules: [
                //     {
                //         required: true,
                //         message: 'Please enter Quantity',
                //     },
                // ],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                disable_filter: true,
                dynamicMeta: (row, original_field_meta) => {
                    let dynamicMetaRow = { ...original_field_meta };

                    let optionsFrCurrSrvcTypeId =
                        quantityOptionsBySrvcTypeId[row.srvc_type];
                    if (optionsFrCurrSrvcTypeId) {
                        dynamicMetaRow['options'] = [
                            ...dynamicMetaRow['options'],
                            ...optionsFrCurrSrvcTypeId,
                        ];
                    }

                    return dynamicMetaRow;
                },
            },
        ];
        // console.log('colMeta-', colMeta);
        return colMeta;
    };

    const getRevenueConfig = ({
        tabKey,
    }: RevenueConfigProps): RevenueConfigItem[] => {
        if (tabKey == 'vertical_type') {
            return [
                {
                    key: 'revenue_column_meta',
                    className: 'gx-d-none',
                    widgetProps: { hidden: true },
                },
                {
                    key: `revenue_column_meta_rules`,
                    render: () => {
                        return (
                            <div className="gx-mb-3">
                                <InputTable
                                    onChange={(newRowData) => {
                                        form.current.setFieldsValue({
                                            revenue_column_meta:
                                                JSON.stringify(newRowData),
                                        });
                                    }}
                                    rowData={getRowDataFrRevenue()}
                                    colMeta={getColMetaFrRevenue()}
                                    removePagination
                                />
                            </div>
                        );
                    },
                },
            ];
        }

        return [
            {
                key: 'srvc_type_revenue_column_meta',
                className: 'gx-d-none',
                widgetProps: { hidden: true },
            },
            {
                key: `revenue_column_meta_rules`,
                render: () => {
                    return (
                        <div className="gx-mb-3">
                            <InputTable
                                onChange={(newRowData) => {
                                    form.current.setFieldsValue({
                                        srvc_type_revenue_column_meta:
                                            JSON.stringify(newRowData),
                                    });
                                }}
                                rowData={getRowDataFrSrvcTypeRevenue()}
                                colMeta={getColMetaFrSrvcTypeRevenue()}
                            />
                        </div>
                    );
                },
            },
        ];
    };

    const getProfitLossConfigMeta = () => {
        const isCheckedProfitLoss = form?.current?.getFieldValue(
            'isCheckedProfitLoss'
        );
        const selectedProfitLossAuthorisedRoles = form?.current?.getFieldValue(
            'profitLossAuthorisedRoles'
        );
        const rolesWhoCanSyncProfitLossValues = roleList.filter(
            (role: { label: string; value: string }) =>
                selectedProfitLossAuthorisedRoles?.includes(role.value)
        );

        return {
            formItemLayout: null as null | Record<string, any>,
            fields: [
                {
                    key: 'isCheckedProfitLoss',
                    label: 'Enable P&L',
                    widget: 'checkbox',
                    onChange: () => refresh(),
                },
                ...(isCheckedProfitLoss
                    ? [
                          {
                              key: `configure_revenue_col`,
                              render: () => {
                                  return (
                                      <h4 className="gx-mb-4">
                                          {' '}
                                          Configure Revenue column values{' '}
                                      </h4>
                                  );
                              },
                          },
                          {
                              key: `configure_revenue_column_values`,
                              render: () => {
                                  return (
                                      //@ts-ignore
                                      <Tabs defaultActiveKey="vertical_type">
                                          {/* @ts-ignore */}
                                          <Tabs.TabPane
                                              tab="Vertical Type"
                                              key="vertical_type"
                                          >
                                              <FormBuilder
                                                  form={form}
                                                  meta={getRevenueConfig({
                                                      tabKey: 'vertical_type',
                                                  })}
                                              />
                                          </Tabs.TabPane>
                                          {/* @ts-ignore */}
                                          <Tabs.TabPane
                                              tab="Service Type"
                                              key="service_type"
                                          >
                                              <FormBuilder
                                                  form={form}
                                                  meta={getRevenueConfig({
                                                      tabKey: 'service_type',
                                                  })}
                                              />
                                          </Tabs.TabPane>
                                      </Tabs>
                                  );
                              },
                          },
                          {
                              key: 'userFieldFrResourceType',
                              label: `Select user field for Resource Type`,
                              widget: 'select',
                              rules: [
                                  {
                                      required: true,
                                      message:
                                          'Please select user field for Resource Type',
                                  },
                              ],
                              widgetProps: { mode: 'single', allowClear: true },
                              options: userFieldFrResourceType(),
                          },
                          {
                              key: 'sp_additional_revenue_field',
                              label: 'Select SP field for additional revenue',
                              widget: 'select',
                              widgetProps: {
                                  allowClear: true,
                                  placeholder:
                                      'Select SP field for additional revenue',
                                  onChange: (value: string | undefined) => {
                                      form.current?.setFieldsValue({
                                          sp_additional_revenue_field:
                                              value ?? null,
                                      });
                                  },
                              },
                              options: getPossibleNumberFields() || [],
                          },
                          {
                              key: 'lambda_arn_profit_loss',
                              label: 'Enter lambda arn for P&L',
                              rules: [
                                  {
                                      validator: validateLambdaArn,
                                      message:
                                          'Please enter a valid Lambda ARN',
                                  },
                              ],
                          },
                          {
                              key: 'profitLossAuthorisedRoles',
                              label: <span>Select roles that can see P&L</span>,
                              widget: 'select',
                              widgetProps: {
                                  mode: 'multiple',
                                  allowClear: true,
                                  showSearch: true,
                                  optionFilterProp: 'children',
                              },
                              onChange: (value: string[]) => {
                                  refresh();
                                  profitLossAuthorisedRolesHandler({
                                      selectedRoles: value,
                                  });
                              },
                              options: roleList || [],
                          },
                          ...(selectedProfitLossAuthorisedRoles?.length > 0
                              ? [
                                    {
                                        key: 'roles_who_can_sync_pl_values',
                                        label: (
                                            <span>Who can sync P&L values</span>
                                        ),
                                        widget: 'select',
                                        widgetProps: {
                                            mode: 'multiple',
                                            allowClear: true,
                                            showSearch: true,
                                            optionFilterProp: 'children',
                                        },
                                        options:
                                            rolesWhoCanSyncProfitLossValues,
                                    },
                                ]
                              : []),
                      ]
                    : []),
            ],
        };
    };

    if (isLoadingViewData) {
        return (
            <div className="gx-w-100 gx-p-2">
                <CircularProgress className="gx-h-100" />
            </div>
        );
    }

    if (apiRespData === undefined) {
        return <p className="gx-text-red">{error}</p>;
    }

    return <FormBuilder meta={getProfitLossConfigMeta()} form={form} />;
};

export default ProfitLossConfig;
