import React, { useEffect, useState } from 'react';
import { Button } from 'antd';
import CustomScrollbars from '../../../util/CustomScrollbars';
import QuickFilters from '../../../components/wify-utils/crud/overview/QuickFilters';
import ConfigHelpers from '../../../util/ConfigHelpers';
import { isMobileView } from '../../../util/helpers';

export const SideBar = ({
    activeFilters: initialFilters,
    onFilterChange,
    filters,
    onAddClick,
}) => {
    const [activeFilters, setActiveFilters] = useState(initialFilters);

    // Sync state when props.activeFilters changes
    useEffect(() => {
        setActiveFilters(initialFilters);
    }, [initialFilters]);

    const handleFilterChange = (newFilterObject) => {
        const updatedFilters = { ...newFilterObject };
        setActiveFilters(updatedFilters);
        onFilterChange(updatedFilters);
    };

    return (
        <div className="gx-module-side">
            <div className="gx-module-side-header">
                <div className="gx-module-logo">
                    <i className="icon icon-contacts gx-mr-4" />
                    <span>Availability</span>
                </div>
            </div>

            <div className="gx-module-side-content">
                <CustomScrollbars className="gx-module-side-scroll">
                    {/* Uncomment if needed */}
                    {/* {!isMobileView() && (
                        <div className="gx-module-add-task">
                            <Button
                                className="gx-btn-block ant-btn"
                                type="primary"
                                aria-label="add"
                                onClick={onAddClick}
                            >
                                <i className="icon icon-signup gx-mr-2" />
                                <span>Add Leave</span>
                            </Button>
                        </div>
                    )} */}
                    <QuickFilters
                        filters={filters}
                        onFilterChange={handleFilterChange}
                        activeFilters={activeFilters}
                        linkMode
                    />
                </CustomScrollbars>
            </div>
        </div>
    );
};

export default SideBar;
