import moment from 'moment';

export const availabilityStatuses = {
    offline: {
        value: 'offline',
        color: 'error',
        label: 'Offline',
    },
    available: {
        value: 'available',
        color: 'success',
        label: 'Available',
    },
    limited: {
        value: 'limited',
        color: 'warning',
        label: 'Limited',
    },
};

export const getStatusCode = (total, selected) => {
    if (total > 0 && total == selected) {
        return availabilityStatuses.available.value;
    }
    if (selected > 0) {
        return availabilityStatuses.limited.value;
    }
    return availabilityStatuses.offline.value;
};

export const isSlotInPast = (dayDateString, slot) => {
    const now = moment();
    const dayMoment = moment(dayDateString, 'YYYY-MM-DD'); // ✅ convert to moment

    if (!dayMoment.isValid()) return false; // safety check

    if (dayMoment.isBefore(now, 'day')) return true;
    if (dayMoment.isAfter(now, 'day')) return false;

    const slotStart = moment(
        `${dayDateString} ${slot.start_time}`,
        'YYYY-MM-DD hh:mm A'
    );
    return slotStart.isBefore(now); // ⛔ can't edit if slot already started
};

export const canUpdateDate = (date) => {
    const dateMoment = moment(date, 'YYYY-MM-DD'); // 🔁 Convert string to moment
    const today = moment().startOf('day');
    return dateMoment.isSameOrAfter(today);
};
