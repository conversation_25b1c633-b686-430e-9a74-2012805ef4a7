import { Card, message, Tabs, Tag, Typography } from 'antd';
import React, { useState } from 'react';
import { convertUTCToDisplayTime, isTodayDate } from '../../util/helpers';
import SingleDay from './SingleDay';
import { CheckOutlined } from '@ant-design/icons';
import moment from 'moment';
import { availabilityStatuses } from './helpers';
import AvailabilityCalendar from './calender';

const Text = Typography.Text;

const generateDays = () => {
    const days = [];
    const currentDate = moment();
    for (let i = 0; i < 4; i++) {
        const date = moment(currentDate).add(i, 'days');
        const dateStr = date.local().format('YYYY-MM-DD');
        const dayName = isTodayDate(dateStr) ? 'Today' : date.format('ddd');
        days.push({
            dayName,
            date: dateStr,
        });
    }
    return days;
};

const MyAvailability = () => {
    const [activeMainTab, setActiveMainTab] = useState('availability');
    const [selectedDay, setSelectedDay] = useState('1');
    const [days, setdays] = useState(generateDays());
    const [dayWiseStatus, setDayWiseStatus] = useState({});

    return (
        <div className="wy-amya-calendar-wrapper">
            <Tabs
                activeKey={activeMainTab}
                onChange={(key) => setActiveMainTab(key)}
            >
                <Tabs.TabPane tab="Availability" key="availability">
                    <Tabs
                        activeKey={selectedDay}
                        onChange={setSelectedDay}
                        key={`availability-tab-${activeMainTab}`} // 👈 key forces re-render
                    >
                        {days.map((day, dayIndex) => {
                            const status =
                                dayWiseStatus[day.date] ||
                                availabilityStatuses.offline.value;
                            return (
                                <Tabs.TabPane
                                    forceRender
                                    key={String(dayIndex + 1)}
                                    tab={
                                        <div className="gx-text-center wy-amya-tab-width">
                                            <Text className="wy-fw-300">
                                                {day.dayName === 'Today'
                                                    ? 'Today'
                                                    : day.dayName.slice(0, 3)}
                                            </Text>
                                            <div className="wy-fw-500 wy-amya-day-date">
                                                {moment(day.date).format('DD')}
                                            </div>
                                            <Tag
                                                size="small"
                                                color={
                                                    availabilityStatuses[status]
                                                        .color
                                                }
                                                className="gx-m-0"
                                            >
                                                {
                                                    availabilityStatuses[status]
                                                        .label
                                                }
                                            </Tag>
                                        </div>
                                    }
                                >
                                    <SingleDay
                                        day={day}
                                        onNewStatus={(statusCode) =>
                                            setDayWiseStatus({
                                                ...dayWiseStatus,
                                                [day.date]: statusCode,
                                            })
                                        }
                                    />
                                </Tabs.TabPane>
                            );
                        })}
                    </Tabs>
                </Tabs.TabPane>
                <Tabs.TabPane tab="Calendar" key="calendar">
                    <AvailabilityCalendar
                        key={`calendar-tab-${activeMainTab}`}
                    />
                </Tabs.TabPane>
            </Tabs>
        </div>
    );
};

export default MyAvailability;
