import React, { Component } from 'react';
import {
    Input,
    Alert,
    Tag,
    Avatar,
    Row,
    Col,
    Dropdown,
    Menu,
    Tooltip,
    Button,
    Badge,
    Switch,
} from 'antd';
import { useState } from 'react';
import {
    AntDesignOutlined,
    CloseCircleOutlined,
    EditOutlined,
    UserOutlined,
    WarningOutlined,
} from '@ant-design/icons';
import Modal from 'antd/lib/modal/Modal';
import { NoData, getRandomTagBgColor } from '../../util/helpers';
import ConfigHelpers from '../../util/ConfigHelpers';
import UserName from '../../components/wify-utils/UserName';
import { SearchOutlined } from '@ant-design/icons';
import RefreshAuthorities from './RefreshAuthorities';
const { Search } = Input;

export default function AuthoritySelector(props) {
    const [form_data, setFormData] = useState(props.initialValue || {});
    const [isModalAuthorityVisible, setIsModalAuthorityVisible] =
        useState(false);
    const [authorityHasChanged, setAuthorityHasChanged] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    const [showAll, setShowAll] = useState(false);

    const showAuthorityModal = () => {
        setIsModalAuthorityVisible(true);
    };

    const handleOk = () => {
        setIsModalAuthorityVisible(false);
        setShowAll(false);
    };

    const handleCancel = () => {
        setIsModalAuthorityVisible(false);
        setShowAll(false);
    };

    const tellParentAboutTheChange = (newFormData) => {
        if (props.onChange) {
            props.onChange(newFormData);
        }
    };

    const noteAuthorityChange = () => {
        setAuthorityHasChanged(true);
    };

    const clearSelectedUser = (singleAuthorityMeta) => {
        let newFormData = { ...form_data };
        newFormData[singleAuthorityMeta.key] = '';
        noteAuthorityChange();
        setFormData(newFormData);
        tellParentAboutTheChange(newFormData);
    };

    const onUserClick = (user_id, role_id) => {
        let newKeyValue = {};
        newKeyValue[role_id] = user_id;
        let newFormData = { ...form_data, ...newKeyValue };
        noteAuthorityChange();
        setFormData(newFormData);
        tellParentAboutTheChange(newFormData);
        setSearchValue('');
        setShowAll(false);
    };

    const getUserName = (user_list, user_id) => {
        return user_list.filter(
            (singleAuthorityMeta) => singleAuthorityMeta.value == user_id
        )?.[0]?.label;
    };

    const getAuthorities = () => {
        // return [];
        return props.meta.filter(
            (singleAuthorityMeta) => form_data[singleAuthorityMeta.key]
        );
    };
    const handleSearch = (e) => {
        const value = e.target.value;
        setSearchValue(value);
    };

    const tellParentToRefreshList = () => {
        if (props.onDataModified != undefined) {
            props.onDataModified(props?.initialValue?.entry_id);
        }
    };
    const handleToggle = () => {
        setShowAll(!showAll);
    };

    const handleVisibleChange = (visible) => {
        if (!visible) {
            setShowAll(false);
            setSearchValue('');
        }
    };

    const getUserListMenu = (singleAuthorityMeta) => {
        const locGroupMatchedUserOptions = singleAuthorityMeta.options.filter(
            (singleUserMeta) => singleUserMeta.is_loc_grp_matched_with_srvc_req
        );
        return (
            <Menu
                onClick={(e) => onUserClick(e.key, singleAuthorityMeta.key)}
                className={
                    singleAuthorityMeta?.options?.length > 10
                        ? 'wy-maxOverflow'
                        : ''
                }
            >
                <div className="wy-admin-selector-wrapper">
                    {singleAuthorityMeta.options.length > 0 && (
                        <Input
                            prefix={<SearchOutlined />}
                            placeholder="Search"
                            className="search-input"
                            onChange={handleSearch}
                            value={searchValue}
                        />
                    )}
                    {singleAuthorityMeta?.is_loc_grp_filteration_enabled_fr_sp && (
                        <div className="gx-d-flex gx-align-items-center wy-gap-10 gx-mt-1 gx-ml-1">
                            <div>
                                <Switch
                                    checked={showAll}
                                    size="small"
                                    onChange={handleToggle}
                                />
                            </div>
                            <div>
                                <p className="gx-mb-0 gx-ml-2">Show all</p>
                            </div>
                        </div>
                    )}
                </div>

                {showAll ||
                !singleAuthorityMeta.is_loc_grp_filteration_enabled_fr_sp ? (
                    singleAuthorityMeta.options.map(
                        (singleUserMeta, index) =>
                            singleUserMeta.label
                                ?.toLowerCase()
                                .includes(searchValue.toLowerCase()) && (
                                <Menu.Item
                                    className="gx-justify-content-start"
                                    key={singleUserMeta.value}
                                >
                                    <Avatar
                                        size={35}
                                        className={`gx-rounded-circle ${singleUserMeta.icon == true ? 'gx-mx-2' : 'gx-mr-2'} `}
                                    >
                                        {singleUserMeta?.label?.substring(0, 1)}
                                    </Avatar>
                                    {singleUserMeta.label}
                                </Menu.Item>
                            )
                    )
                ) : locGroupMatchedUserOptions.length > 0 ? (
                    locGroupMatchedUserOptions.map(
                        (filteredUserMeta, index) =>
                            filteredUserMeta.label
                                ?.toLowerCase()
                                .includes(searchValue.toLowerCase()) && (
                                <Menu.Item key={filteredUserMeta.value}>
                                    <Avatar
                                        size={35}
                                        className={`gx-rounded-circle ${filteredUserMeta.icon == true ? 'gx-mx-2' : 'gx-mr-2'} `}
                                    >
                                        {filteredUserMeta?.label?.substring(
                                            0,
                                            1
                                        )}
                                    </Avatar>
                                    {filteredUserMeta.label ? (
                                        filteredUserMeta.label
                                    ) : (
                                        <NoData />
                                    )}
                                </Menu.Item>
                            )
                    )
                ) : (
                    <div>
                        <NoData />
                    </div>
                )}
            </Menu>
        );
    };
    // console.log("authority",form_data);
    return (
        <div>
            {props.debugMode && (
                <div>
                    <Alert message="Running in DEMO MODE!!" />
                    <Input.TextArea
                        className="gx-bg-dark gx-text-white"
                        autoSize={{ minRows: 2, maxRows: 30 }}
                        value={JSON.stringify(form_data)}
                    />
                </div>
            )}
            <div className="scrollable_row">
                <div justify="center">
                    <div className="gx-p-0">
                        <div className="wy-flex-responsive gx-justify-content-between gx-pr-3 gx-flex-row">
                            {getAuthorities().length > 0 ? (
                                <>
                                    <p className="gx-mt-2 gx-text-muted">
                                        {props?.spAuthority ? (
                                            'Service Provider Authorities'
                                        ) : (
                                            <>
                                                Brand Authorities
                                                {props?.initialValue
                                                    ?.cust_pincode &&
                                                    !props?.isCustomerRequests &&
                                                    props.is_enable_auto_assign_authorities_refresh_btn && (
                                                        <RefreshAuthorities
                                                            srvc_req_id={
                                                                props
                                                                    ?.initialValue
                                                                    ?.entry_id
                                                            }
                                                            srvc_type_id={
                                                                props
                                                                    ?.initialValue
                                                                    ?.srvc_type_id
                                                            }
                                                            allowEdit={
                                                                props.allowEdit
                                                            }
                                                            spAuthority={
                                                                props?.spAuthority
                                                            }
                                                            onChange={() => {
                                                                tellParentToRefreshList();
                                                            }}
                                                        />
                                                    )}
                                            </>
                                        )}
                                    </p>
                                </>
                            ) : (
                                <p className="gx-mb-0 gx-my-2 gx-text-dark gx-d-flex gx-align-items-center">
                                    <WarningOutlined className="gx-text-dark gx-mr-1 gx-text-red" />
                                    {props?.spAuthority ? (
                                        ' No Service Provider Authorities selected '
                                    ) : (
                                        <>
                                            No Authorities selected
                                            {props?.initialValue
                                                ?.cust_pincode &&
                                                !props?.isCustomerRequests &&
                                                props.is_enable_auto_assign_authorities_refresh_btn && (
                                                    <RefreshAuthorities
                                                        srvc_req_id={
                                                            props?.initialValue
                                                                ?.entry_id
                                                        }
                                                        srvc_type_id={
                                                            props?.initialValue
                                                                ?.srvc_type_id
                                                        }
                                                        allowEdit={
                                                            props.allowEdit
                                                        }
                                                        spAuthority={
                                                            props?.spAuthority
                                                        }
                                                        onChange={() => {
                                                            tellParentToRefreshList();
                                                        }}
                                                    />
                                                )}
                                        </>
                                    )}
                                </p>
                            )}
                            {(props.allowEdit ||
                                !props?.isCustomerRequests) && (
                                <Button
                                    type="link"
                                    className="gx-mb-0 gx-p-0"
                                    onClick={showAuthorityModal}
                                    disabled={!props.allowEdit}
                                >
                                    <EditOutlined />
                                </Button>
                            )}
                        </div>
                        {getAuthorities().length > 0 && (
                            <>
                                <div className="scrolling_wrapper gx-mr-3 gx-mb-2">
                                    {getAuthorities().map(
                                        (singleAuthorityMeta) => (
                                            <Tooltip
                                                title={() => {
                                                    return (
                                                        <UserName
                                                            id={
                                                                form_data[
                                                                    singleAuthorityMeta
                                                                        .key
                                                                ]
                                                            }
                                                            customError={
                                                                'Missing'
                                                            }
                                                        />
                                                    );
                                                }}
                                                placement="top"
                                            >
                                                <div className="gx-text-center gx-d-inline-block">
                                                    <Tag
                                                        color={`${getRandomTagBgColor()}`}
                                                        className="wy-tag-line-height-adjust"
                                                    >
                                                        <UserName
                                                            id={
                                                                form_data[
                                                                    singleAuthorityMeta
                                                                        .key
                                                                ]
                                                            }
                                                            customError={'-'}
                                                            showInActive
                                                        />
                                                        <br />
                                                        <small className="gx-mb-0 gx-text-center">
                                                            {
                                                                singleAuthorityMeta.label
                                                            }
                                                        </small>
                                                    </Tag>
                                                </div>
                                            </Tooltip>
                                        )
                                    )}
                                </div>
                            </>
                        )}

                        {authorityHasChanged && (
                            <div className="gx-mr-3">
                                <Alert
                                    message="**Unsaved changes(Click save to apply)"
                                    type="warning"
                                />
                            </div>
                        )}
                    </div>
                    <div></div>
                </div>
            </div>
            <Modal
                title="All Authorities"
                visible={isModalAuthorityVisible}
                onOk={handleOk}
                onCancel={handleCancel}
            >
                <Row gutter={0} className="gx-mx-2 gx-my-3 ">
                    {props.meta.map((singleAuthorityMeta) => (
                        <Col xs={24} md={12} key={singleAuthorityMeta.key}>
                            <div
                                className={
                                    form_data[singleAuthorityMeta.key]
                                        ? 'gx-text-center gx-border gx-p-2 gx-my-2 gx-rounded-lg gx-border-success'
                                        : 'gx-text-center gx-border gx-p-2 gx-my-2 gx-rounded-lg gx-border-danger'
                                }
                            >
                                <Dropdown
                                    overlay={getUserListMenu(
                                        singleAuthorityMeta
                                    )}
                                    trigger={['click']}
                                    onVisibleChange={handleVisibleChange}
                                    placement="bottomCenter"
                                >
                                    {form_data[singleAuthorityMeta.key] ? (
                                        <div>
                                            <Tooltip
                                                title={getUserName(
                                                    singleAuthorityMeta.options,
                                                    form_data[
                                                        singleAuthorityMeta.key
                                                    ]
                                                )}
                                            >
                                                <Avatar
                                                    size={35}
                                                    className="gx-rounded-circle"
                                                >
                                                    {getUserName(
                                                        singleAuthorityMeta.options,
                                                        form_data[
                                                            singleAuthorityMeta
                                                                .key
                                                        ]
                                                    )?.substring(0, 1)}
                                                </Avatar>
                                            </Tooltip>
                                        </div>
                                    ) : (
                                        <Avatar
                                            size={45}
                                            src={
                                                'https://cdn2.iconfinder.com/data/icons/circles-2/100/sign-dotted-plus-512.png'
                                            }
                                            className="gx-rounded-circle wy-cursor-pointer"
                                            alt=""
                                        />
                                    )}
                                </Dropdown>
                                {getUserName(
                                    singleAuthorityMeta.options,
                                    form_data[singleAuthorityMeta.key]
                                ) ? (
                                    <span>
                                        {
                                            getUserName(
                                                singleAuthorityMeta.options,
                                                form_data[
                                                    singleAuthorityMeta.key
                                                ]
                                            )?.split(' ')[0]
                                        }
                                        <CloseCircleOutlined
                                            className="gx-ml-1 gx-text-danger gx-my-2"
                                            onClick={(e) =>
                                                clearSelectedUser(
                                                    singleAuthorityMeta
                                                )
                                            }
                                        />
                                    </span>
                                ) : (
                                    <br />
                                )}
                                <br />
                                <Tag
                                    color={'green'}
                                    className="gx-mt-1 gx-mx-0"
                                    style={{ whiteSpace: 'inherit' }}
                                >
                                    {singleAuthorityMeta.label}
                                </Tag>
                            </div>
                        </Col>
                    ))}
                </Row>
            </Modal>
        </div>
    );
}
