import { <PERSON><PERSON>, Col, Collapse, Form, message, Modal, Row, Spin } from 'antd';
import FormBuilder from 'antd-form-builder';
import _, { values } from 'lodash';
import React, { useEffect, useState } from 'react';
import CircularProgress from '../../../../components/CircularProgress';
import {
    decodeFieldsMetaFrmJson,
    decodeFileSectionsFrmJson,
    decodeMicSectionsFrmJson,
} from '../../../../components/wify-utils/FieldCreator/helpers';
import S3Uploader from '../../../../components/wify-utils/S3Uploader/S3Uploader';
import SliderWidget from '../../../../components/wify-utils/SliderWidget';
import ConfigHelpers from '../../../../util/ConfigHelpers';
import {
    convertDateFieldsToMoments,
    convertMomentToDateString,
    convertMomentToLocalDateString,
    convertMomentToLocalDateString1,
    convertMomentToMonthDateAndYearFormat,
    convertValuesOfObjToMoment,
    getCurrentDateAndTimeFrDisplay,
    getValueDataFrmFormMeta,
    getViewModeMetaFrFormMeta,
    isMobileView,
    parseFormulaToString,
    parseFormulaToValue,
    setRemarkFieldAsNoRemarkIfEmpty,
} from '../../../../util/helpers';
import http_utils from '../../../../util/http_utils';
import { getCustomPrefixNameFrUploadingFiles } from '../../../../util/helpers';
import AttachmentsPreview from '../deployment/AttachmentPreview';
import { getAllMergedAttachments } from '../../helpers';
import MicInputV2 from '../../../../components/wify-utils/MicInput_v2';

const sbtskDetailsUrl = '/services/sbtskdetails/attachment';
const formatter = (value) => `${value}%`;

const DailyReqStatusEditor = ({
    editorDayDetails,
    srvcConfigData,
    reqData,
    onChange,
    urlToSubmitFrUpdates,
    isSrvcPrvdrTab,
}) => {
    const day = editorDayDetails.assignee_date;
    const srvcFormData = reqData.form_data;
    let existingDailyStatusObj =
        (isSrvcPrvdrTab
            ? srvcFormData?.sp_daily_status_updates
            : srvcFormData?.daily_status_updates) || {};
    //converting obj to moment at initially because antd date.clone issue raises.
    existingDailyStatusObj[day] = convertValuesOfObjToMoment(
        existingDailyStatusObj[day]
    );

    const [form] = Form.useForm();
    const [error, setError] = useState(undefined);
    const [
        errorFrloadingSbtskDetailViewData,
        setErrorFrLoadingSbtskDetailViewData,
    ] = useState(undefined);
    const forceUpdate = FormBuilder.useForceUpdate();
    const [isFormSubmitting, setIsFormSubmitting] = useState(false);
    const [fileSections, setFileSections] = useState([]); //Use karna hai
    const [filesBySection, setFilesBySection] = useState({});
    const [sectionWiseUploaderReady, setSectionWiseUploaderReady] = useState(
        {}
    );
    const [micRecordingsBySection, setMicRecordingsBySection] = useState({});
    const [sectionWiseMicUploaderReady, setSectionWiseMicUploaderReady] =
        useState({});
    const [allFileUploadersReady, setAllFileUploadersReady] = useState(true);
    const [allMicRecordingsUploadersReady, setAllMicRecordingsUploadersReady] =
        useState(true);
    const [showUploadedFilesModal, setShowUploadedFilesModal] = useState();
    const [sbtskDetailViewData, setSbtskDetailViewData] = useState(undefined);
    const [loadingSbtskDetailViewData, setLoadingSbtskDetailViewData] =
        useState(false);
    const [isFormChanged, setIsFormChanged] = useState(false);
    const [isErrorShown, setIsErrorShown] = useState(false);
    const [selectedImage, setSelectedImage] = useState({});
    let selectedFilesBySection = {};
    // const prefillsrvcFormData =  26132
    // console.log('DailyReqStatusEditor - srvcConfigData',srvcConfigData);
    // console.log('DailyReqStatusEditor : reqData',reqData);
    const rolesWhoHaveEditAccess =
        srvcConfigData?.daily_update_who_can_edit || [];
    const canCurrentUserEdit = ConfigHelpers?.doesUserHaveOneOfTheRole(
        rolesWhoHaveEditAccess
    );
    let timeoutInstance;
    // console.log('DailyReqStatusEditor - canCurrentUserEdit',canCurrentUserEdit);

    useEffect(() => {
        forceUpdate();
        setSlidersInitialValue();
    }, []); // Refresh form after first render for dynamic form elements

    const dailyUpdateIssueFormMeta = () => {
        let dailyUpdateIssueFormMeta = decodeFieldsMetaFrmJson(
            srvcConfigData?.daily_update_issue_form_fields
        );
        return dailyUpdateIssueFormMeta;
    };

    const dailyUpdateFormMeta = () => {
        let dailyUpdateFormMeta = isSrvcPrvdrTab
            ? decodeFieldsMetaFrmJson(
                  srvcConfigData?.sp_daily_update_form_fields
              )
            : decodeFieldsMetaFrmJson(srvcConfigData?.daily_update_form_fields);
        return dailyUpdateFormMeta;
    };

    const setSlidersInitialValue = () => {
        let lineItemWiseProgressFields = getLineItemWiseProgressMeta();

        //set slider value to lower limit when you open it so if you change only one slider and save. All sliders will be saved with current values instead of 0
        lineItemWiseProgressFields.forEach((singleLineItemMeta) => {
            let lowerLimit = getLowerLimitFrSlider(day, singleLineItemMeta.key);
            //ssetting slider if day has already been saved before
            if (
                existingDailyStatusObj[day] != undefined &&
                existingDailyStatusObj[day][singleLineItemMeta.key] == undefined
            ) {
                form.setFieldsValue({
                    [singleLineItemMeta.key]: lowerLimit,
                });
                //setting for day which has never been saved before
            } else if (existingDailyStatusObj[day] == undefined) {
                form.setFieldsValue({
                    [singleLineItemMeta.key]: lowerLimit,
                });
            }
        });

        //setting day progress sliders just like line items sliders
        let lowerLimitDailyProgress = getLowerLimitFrSlider(
            day,
            'day_progress'
        );
        if (
            existingDailyStatusObj[day] != undefined &&
            existingDailyStatusObj[day]['day_progress'] == undefined
        ) {
            form.setFieldsValue({
                day_progress: lowerLimitDailyProgress,
            });
        } else if (existingDailyStatusObj[day] == undefined) {
            form.setFieldsValue({
                day_progress: lowerLimitDailyProgress,
            });
        }
    };

    //find closest before date so lower limit can be found to set the initial value of the slider
    const findclosestBeforeDate = (date) => {
        let allDates = Object.keys(existingDailyStatusObj);

        let closestBefore = null;
        let indexIfDateExists = allDates.findIndex(function (key) {
            return key === date;
        });
        if (date > allDates[allDates.length - 1]) {
            closestBefore = allDates[allDates.length - 1];
            return closestBefore;
        } else if (date <= allDates[0]) {
            closestBefore = 0;
            return closestBefore;
        }

        closestBefore =
            allDates[
                allDates.findIndex(function (key) {
                    return key > date;
                }) - 1
            ];

        if (indexIfDateExists >= 1) {
            closestBefore = allDates[indexIfDateExists - 1];
        }

        return closestBefore;
    };

    //lower limit for setting sliders in useeffect
    const getLowerLimitFrSlider = (day, fieldKey) => {
        let closestBefore = findclosestBeforeDate(day);
        let prevDateData;

        let lowerLimit = 0;

        if (closestBefore) {
            prevDateData = existingDailyStatusObj[closestBefore];
            lowerLimit = prevDateData?.[fieldKey];
        }
        return lowerLimit;
    };

    const calculateDayProgress = (value, fieldKey, lowerLimit, upperLimit) => {
        let countOfItems = 0;
        let totalProgOfItems = 0;
        const lineItemWiseProgressFields = getLineItemWiseProgressMeta();
        lineItemWiseProgressFields.forEach((singleLineItemMeta) => {
            let lineItemProg = form.getFieldValue(singleLineItemMeta.key) || 0;
            totalProgOfItems = totalProgOfItems + lineItemProg;
            countOfItems++;
        });
        const prog = Math.round(totalProgOfItems / countOfItems);
        form.setFieldsValue({
            day_progress: prog,
        });
    };

    const checkFrProgressionAcrossRange = (newVal, day, fieldKey) => {
        //creating a sorted copy of existingdailyobj to perform operations
        const sortedExistingDailyStatusObj = {};

        Object.keys(existingDailyStatusObj)
            .sort(
                (a, b) => existingDailyStatusObj[a] - existingDailyStatusObj[b]
            )
            .forEach((key) => {
                sortedExistingDailyStatusObj[key] = existingDailyStatusObj[key];
            });

        let dates = Object.keys(sortedExistingDailyStatusObj) || [];
        if (dates.length == 0) {
            return;
        }

        // sort the existing dail status obj
        let copyOfExisitingDailyStatusObj = _.cloneDeepWith(
            sortedExistingDailyStatusObj
        );
        if (copyOfExisitingDailyStatusObj[day] == undefined) {
            copyOfExisitingDailyStatusObj[day] = {};
        }
        copyOfExisitingDailyStatusObj[day][fieldKey] = newVal;

        // Loop on the days serially and get the first day at which the progression fails

        let days = Object.keys(copyOfExisitingDailyStatusObj);
        days.sort();

        let firstDay = days[0];
        let previousValue = copyOfExisitingDailyStatusObj[firstDay][fieldKey];

        let msgCount = 0;

        days.map((date) => {
            if (copyOfExisitingDailyStatusObj[date][fieldKey] < previousValue) {
                if (!isErrorShown) {
                    message.error(
                        `${date} progress is lower than previous date, please fix this first before update`
                    );
                    setIsErrorShown(true);
                }
                msgCount++;
            }
            previousValue = copyOfExisitingDailyStatusObj[date][fieldKey];
        });
        if (timeoutInstance) {
            clearTimeout(timeoutInstance);
        }
        timeoutInstance = setTimeout(() => {
            if (msgCount > 0) {
                form.setFieldsValue({
                    [fieldKey]:
                        sortedExistingDailyStatusObj?.[day]?.[fieldKey] ||
                        getLowerLimitFrSlider(day, fieldKey),
                });
                setIsErrorShown(false);
            } else {
                form.setFieldsValue({
                    [fieldKey]: newVal,
                });
            }
            calculateDayProgress(newVal, fieldKey);
        }, 500);
    };

    const getLineItemWiseProgressMeta = () => {
        let lineItemsWiseMeta = [];
        let reqLineItemsFormData = isSrvcPrvdrTab
            ? reqData?.form_data?.sp_line_items?.form_data
            : reqData?.form_data?.line_items?.form_data;
        if (reqLineItemsFormData) {
            let lineItemConfig = JSON.parse(
                srvcConfigData?.srvc_type_line_item_config
            );
            Object.keys(lineItemConfig).forEach((lineItemGrpKey) => {
                let groupConfig = lineItemConfig[lineItemGrpKey];
                let nameFieldFormula = groupConfig.name_field_formula;
                const fields =
                    decodeFieldsMetaFrmJson(groupConfig.fields) || [];
                const idVsLabelMapping = {};
                fields.forEach((singleFieldMeta) => {
                    idVsLabelMapping[singleFieldMeta.label] =
                        singleFieldMeta.key;
                });

                let requestLineItemsInGrp =
                    reqLineItemsFormData[lineItemGrpKey];
                if (
                    requestLineItemsInGrp &&
                    Array.isArray(requestLineItemsInGrp)
                ) {
                    requestLineItemsInGrp.forEach(
                        (singleLineItemInGrp, index) => {
                            let label = `Item ${index + 1}`;
                            let valueDataFrLineItem = getValueDataFrmFormMeta(
                                fields,
                                singleLineItemInGrp
                            );
                            if (
                                nameFieldFormula &&
                                nameFieldFormula.length > 0
                            ) {
                                label = parseFormulaToString(
                                    nameFieldFormula,
                                    idVsLabelMapping,
                                    valueDataFrLineItem
                                );
                            }
                            let itemProgressKey = `progress_${singleLineItemInGrp.input_table_id}`;
                            lineItemsWiseMeta.push({
                                key: itemProgressKey,
                                label: label, // parsed from formula,
                                title: label,
                                widget: SliderWidget,
                                widgetProps: {
                                    tooltip: { formatter: formatter },
                                    onChange: (value) => {
                                        // check for progression
                                        if (
                                            srvcConfigData.daily_progress_update_mode ==
                                            'strict_mode'
                                        ) {
                                            checkFrProgressionAcrossRange(
                                                value,
                                                day,
                                                itemProgressKey
                                            );
                                        }
                                        calculateDayProgress(
                                            value,
                                            itemProgressKey
                                        );
                                    },
                                },
                            });
                        }
                    );
                }
            });
        }
        return lineItemsWiseMeta;
    };
    const getSelectedLineItemWiseProgressMeta = (selectedLineItemIds) => {
        let lineItemsWiseMeta = [];

        if (!selectedLineItemIds || selectedLineItemIds.length === 0) {
            return lineItemsWiseMeta;
        }

        let reqLineItemsFormData = isSrvcPrvdrTab
            ? reqData?.form_data?.sp_line_items?.form_data
            : reqData?.form_data?.line_items?.form_data;

        if (reqLineItemsFormData) {
            let lineItemConfig = JSON.parse(
                srvcConfigData?.srvc_type_line_item_config
            );

            Object.keys(lineItemConfig).forEach((lineItemGrpKey) => {
                let groupConfig = lineItemConfig[lineItemGrpKey];
                let nameFieldFormula = groupConfig.name_field_formula;
                const fields =
                    decodeFieldsMetaFrmJson(groupConfig.fields) || [];
                const idVsLabelMapping = {};
                fields.forEach((singleFieldMeta) => {
                    idVsLabelMapping[singleFieldMeta.label] =
                        singleFieldMeta.key;
                });

                let requestLineItemsInGrp =
                    reqLineItemsFormData[lineItemGrpKey];
                if (
                    requestLineItemsInGrp &&
                    Array.isArray(requestLineItemsInGrp)
                ) {
                    requestLineItemsInGrp.forEach(
                        (singleLineItemInGrp, index) => {
                            // Check if this line item is in the selected items
                            const lineItemId = `${lineItemGrpKey}_${singleLineItemInGrp.input_table_id}`;
                            if (!selectedLineItemIds.includes(lineItemId)) {
                                return; // Skip if not selected
                            }

                            let label = `Item ${index + 1}`;
                            let valueDataFrLineItem = getValueDataFrmFormMeta(
                                fields,
                                singleLineItemInGrp
                            );
                            if (
                                nameFieldFormula &&
                                nameFieldFormula.length > 0
                            ) {
                                label = parseFormulaToString(
                                    nameFieldFormula,
                                    idVsLabelMapping,
                                    valueDataFrLineItem
                                );
                            }
                            let itemProgressKey = `progress_${singleLineItemInGrp.input_table_id}`;
                            lineItemsWiseMeta.push({
                                key: itemProgressKey,
                                className: 'wy-daily-update-line-item-progress',
                                label: label, // parsed from formula,
                                title: label,
                                widget: SliderWidget,
                                widgetProps: {
                                    tooltip: { formatter: formatter },

                                    onChange: (value) => {
                                        // check for progression
                                        if (
                                            srvcConfigData.daily_progress_update_mode ==
                                            'strict_mode'
                                        ) {
                                            checkFrProgressionAcrossRange(
                                                value,
                                                day,
                                                itemProgressKey
                                            );
                                        }
                                        calculateDayProgress(
                                            value,
                                            itemProgressKey
                                        );
                                    },
                                },
                            });
                        }
                    );
                }
            });
        }
        return lineItemsWiseMeta;
    };

    const handleOkFrUploadedFilesModal = (initialFiles, key) => {
        setShowUploadedFilesModal('');

        if (selectedFilesBySection[key] && filesBySection[key]) {
            let removedCount = 0;
            filesBySection[key] = filesBySection[key].filter((element) => {
                if (selectedFilesBySection[key].includes(element)) {
                    removedCount++;
                    return false; // Exclude element from the filtered array
                }
                return true; // Include element in the filtered array
            });
            let newSelectedFilesBySection = _.cloneDeep(filesBySection);
            if (removedCount == 0) {
                newSelectedFilesBySection[key] = [
                    ...new Set(
                        newSelectedFilesBySection[key].concat(
                            selectedFilesBySection[key]
                        )
                    ),
                ];
            } else {
                newSelectedFilesBySection[key] = [
                    ...newSelectedFilesBySection[key],
                ];
            }
            setFilesBySection(newSelectedFilesBySection);
            setSelectedImage(newSelectedFilesBySection);
            onFormValueChange();
        } else if (selectedFilesBySection[key]) {
            let newSelectedFilesBySection = _.cloneDeep(filesBySection);
            newSelectedFilesBySection[key] = [
                ...selectedFilesBySection[key],
                ...initialFiles,
            ];
            setFilesBySection(newSelectedFilesBySection);
            setSelectedImage(newSelectedFilesBySection);
            onFormValueChange();
        }
    };

    const selectFromUploadedFilesClick = (sbtskDetailViewData) => {
        // console.log('check',reqData.form_data)
        if (sbtskDetailViewData == undefined) {
            setLoadingSbtskDetailViewData(true);
            setErrorFrLoadingSbtskDetailViewData(undefined);
            var params = {};

            const onComplete = (resp) => {
                setSbtskDetailViewData(resp.data);
                setLoadingSbtskDetailViewData(false);
            };

            const onError = (error) => {
                message.error('Unable to load');
                setErrorFrLoadingSbtskDetailViewData(
                    http_utils.decodeErrorToMessage(error)
                );
                setLoadingSbtskDetailViewData(false);
            };
            let url =
                sbtskDetailsUrl +
                '/' +
                reqData.id +
                '/' +
                editorDayDetails.assignee_date;
            // console.log("url",url);
            http_utils.performGetCall(url, params, onComplete, onError);
        }
    };

    const noteSelectedFilesBySection = (fileSection, files) => {
        if (selectedFilesBySection[fileSection] == undefined) {
            selectedFilesBySection[fileSection] = [files];
        } else if (!selectedFilesBySection[fileSection]?.includes(files)) {
            selectedFilesBySection[fileSection].push(files);
        } else {
            const index = selectedFilesBySection[fileSection]?.indexOf(files);
            if (index > -1) {
                // only splice array when item is found
                selectedFilesBySection[fileSection].splice(index, 1); // 2nd parameter means remove one item only
            }
        }
    };

    const hasFacedIssueForDay = () =>
        (form.getFieldValue('issue_faced') || 'No') == 'Yes';

    // Function to check if files are selected and update required property
    const handleFileSectionRequiredStatus = (singleFileSection) => {
        const filesSelected =
            selectedImage?.[singleFileSection.key]?.length > 0;
        singleFileSection.required = filesSelected
            ? false
            : singleFileSection.required;
    };

    const getLineItemOptions = () => {
        let options = [];
        let lineItemsData = isSrvcPrvdrTab
            ? reqData?.form_data?.sp_line_items?.form_data
            : reqData?.form_data?.line_items?.form_data;

        if (!lineItemsData) {
            return options;
        }

        let lineItemConfig = {};
        try {
            lineItemConfig = JSON.parse(
                srvcConfigData?.srvc_type_line_item_config || '{}'
            );
        } catch (e) {
            console.log(
                'DailyReqStatusEditor :: getLineItemOptions :: Error ::',
                e
            );
            return options;
        }

        // Process each line item group
        Object.keys(lineItemConfig).forEach((lineItemGrpKey) => {
            if (
                !lineItemsData[lineItemGrpKey] ||
                !Array.isArray(lineItemsData[lineItemGrpKey])
            ) {
                return;
            }

            let groupConfig = lineItemConfig[lineItemGrpKey] || {};
            let nameFieldFormula = groupConfig.name_field_formula;
            const fields = decodeFieldsMetaFrmJson(groupConfig.fields) || [];

            // Create a mapping of field labels to keys
            const idVsLabelMapping = {};
            fields.forEach((singleFieldMeta) => {
                idVsLabelMapping[singleFieldMeta.label] = singleFieldMeta.key;
            });

            // Process each line item in the group
            lineItemsData[lineItemGrpKey].forEach((singleLineItem, index) => {
                if (!singleLineItem || !singleLineItem.input_table_id) {
                    return;
                }

                let label = `${groupConfig.label || 'Item'} ${index + 1}`;
                let valueData = getValueDataFrmFormMeta(fields, singleLineItem);

                // Try to use the name formula if available
                if (nameFieldFormula && nameFieldFormula.length > 0) {
                    try {
                        label = parseFormulaToString(
                            nameFieldFormula,
                            idVsLabelMapping,
                            valueData
                        );
                    } catch (e) {
                        console.log(
                            'DailyReqStatusEditor :: getLineItemOptions :: Error ::',
                            e
                        );
                    }
                }

                // Add the option
                options.push({
                    label: label,
                    value: `${lineItemGrpKey}_${singleLineItem.input_table_id}`,
                });
            });
        });

        return options;
    };

    const meta = () => {
        let dailyUpdateWillHaveIssues =
            srvcConfigData?.daily_update_will_have_issues == 'Yes';
        let trackLineItemWiseProgress =
            srvcConfigData?.daily_update_track_line_item_progress == 'Yes';
        let showLineItemBySelection =
            srvcConfigData?.show_line_item_by_selection;
        let trackLineItemWisePhotos =
            srvcConfigData?.daily_update_dynamic_line_item_wise_files == 'Yes';
        let selectedLineItemsFrProgress = form.getFieldValue(
            'selected_line_items'
        );

        return {
            formItemLayout: null,
            fields: [
                {
                    key: 'day_remarks',
                    label: 'Work done today',
                    widget: 'textarea',
                },
                {
                    render: () => <hr />,
                },
                ...(dailyUpdateWillHaveIssues
                    ? [
                          {
                              key: 'issue_faced',
                              label: 'Issue faced ?',
                              widget: 'radio-group',
                              options: ['Yes', 'No'],
                              onChange: () => {
                                  forceUpdate();
                              },
                              required: true,
                          },
                      ]
                    : []),
                ...(hasFacedIssueForDay()
                    ? [
                          {
                              render: () => <hr />,
                          },
                          ...dailyUpdateIssueFormMeta(),
                          {
                              render: () => (
                                  <>
                                      <Row>
                                          {getIssueFileMeta()?.map(
                                              (singleFileSection, index) => {
                                                  handleFileSectionRequiredStatus(
                                                      singleFileSection
                                                  );
                                                  return (
                                                      <Col
                                                          xs={24}
                                                          md={24}
                                                          className="gx-pl-0"
                                                          key={
                                                              singleFileSection.key
                                                          }
                                                      >
                                                          {singleFileSection.title !=
                                                              '' && (
                                                              <h3 className="gx-mt-3">
                                                                  {singleFileSection.required && (
                                                                      <span
                                                                          style={{
                                                                              color: 'red',
                                                                          }}
                                                                      >
                                                                          {' '}
                                                                          *{' '}
                                                                      </span>
                                                                  )}
                                                                  {
                                                                      singleFileSection.title
                                                                  }
                                                                  <hr className="gx-bg-dark"></hr>
                                                              </h3>
                                                          )}
                                                          <Form.Item
                                                              name={
                                                                  'file_uploads'
                                                              }
                                                          >
                                                              {
                                                                  <>
                                                                      <Button
                                                                          type="link"
                                                                          onClick={() => {
                                                                              setShowUploadedFilesModal(
                                                                                  singleFileSection.key
                                                                              );
                                                                              selectFromUploadedFilesClick(
                                                                                  sbtskDetailViewData
                                                                              );
                                                                          }}
                                                                      >
                                                                          Select
                                                                          from
                                                                          uploaded
                                                                          files
                                                                      </Button>

                                                                      <Modal
                                                                          title="Attachments"
                                                                          centered
                                                                          visible={
                                                                              singleFileSection.key ==
                                                                              showUploadedFilesModal
                                                                          }
                                                                          onOk={() => {
                                                                              handleOkFrUploadedFilesModal(
                                                                                  prefillFormData
                                                                                      .attachments?.[
                                                                                      singleFileSection
                                                                                          .key
                                                                                  ] ||
                                                                                      [],
                                                                                  singleFileSection.key
                                                                              );
                                                                          }}
                                                                          onCancel={() =>
                                                                              setShowUploadedFilesModal(
                                                                                  ''
                                                                              )
                                                                          }
                                                                          width={
                                                                              1000
                                                                          }
                                                                      >
                                                                          {loadingSbtskDetailViewData ? (
                                                                              <div className="gx-loader-view gx-loader-position">
                                                                                  <CircularProgress />
                                                                              </div>
                                                                          ) : sbtskDetailViewData ==
                                                                            undefined ? (
                                                                              <p className="gx-text-red">
                                                                                  {
                                                                                      errorFrloadingSbtskDetailViewData
                                                                                  }
                                                                              </p>
                                                                          ) : (
                                                                              <AttachmentsPreview
                                                                                  attachments={getAllMergedAttachments(
                                                                                      sbtskDetailViewData,
                                                                                      prefillFormData
                                                                                          .attachments?.[
                                                                                          singleFileSection
                                                                                              .key
                                                                                      ] ||
                                                                                          []
                                                                                  )}
                                                                                  noteSelectedFilesBySection={(
                                                                                      files
                                                                                  ) => {
                                                                                      noteSelectedFilesBySection(
                                                                                          singleFileSection.key,
                                                                                          files
                                                                                      );
                                                                                  }}
                                                                              />
                                                                          )}
                                                                      </Modal>
                                                                      {
                                                                          selectedImage[
                                                                              singleFileSection
                                                                                  .key
                                                                          ] && (
                                                                              <div
                                                                                  key={
                                                                                      index
                                                                                  }
                                                                              >
                                                                                  <AttachmentsPreview
                                                                                      attachments={
                                                                                          selectedImage[
                                                                                              singleFileSection
                                                                                                  .key
                                                                                          ]
                                                                                      }
                                                                                      noteSelectedFilesBySection={(
                                                                                          files
                                                                                      ) => {
                                                                                          noteSelectedFilesBySection(
                                                                                              singleFileSection.key,
                                                                                              files
                                                                                          );
                                                                                      }}
                                                                                      selectOnly={
                                                                                          false
                                                                                      }
                                                                                      sectionKey={
                                                                                          singleFileSection.key
                                                                                      }
                                                                                      prevUploadedFiles={
                                                                                          prefillFormData
                                                                                              ?.attachments?.[
                                                                                              singleFileSection
                                                                                                  .key
                                                                                          ]
                                                                                      }
                                                                                  />
                                                                              </div>
                                                                          )
                                                                          // ))
                                                                      }
                                                                  </>
                                                              }
                                                              <S3Uploader
                                                                  // className="gx-w-50"
                                                                  // demoMode
                                                                  required={
                                                                      singleFileSection.required
                                                                  }
                                                                  maxColSpan={4}
                                                                  authToken={http_utils.getAuthToken()}
                                                                  prefixDomain={http_utils.getCDNDomain()}
                                                                  customFilePrefixName={getCustomPrefixNameFrUploadingFiles()}
                                                                  totalFiles={
                                                                      prefillFormData
                                                                          ?.attachments?.[
                                                                          singleFileSection
                                                                              .key
                                                                      ]
                                                                  }
                                                                  onFilesChanged={(
                                                                      files,
                                                                      deletedFileUrl
                                                                  ) => {
                                                                      onFilesChanged(
                                                                          singleFileSection.key,
                                                                          files,
                                                                          deletedFileUrl
                                                                      );
                                                                  }}
                                                                  onReadyStatusChanged={(
                                                                      isReady
                                                                  ) => {
                                                                      onFileUploaderReadyChange(
                                                                          singleFileSection.key,
                                                                          isReady
                                                                      );
                                                                  }}
                                                                  initialFiles={
                                                                      prefillFormData
                                                                          .attachments?.[
                                                                          singleFileSection
                                                                              .key
                                                                      ] || []
                                                                  }
                                                                  customPreviewHeight="100%"
                                                                  customFileIconMaxWidth="40px"
                                                                  compConfig={{
                                                                      name: 'daily-req-status-modal-preview',
                                                                  }}
                                                              />
                                                          </Form.Item>
                                                      </Col>
                                                  );
                                              }
                                          )}
                                          {getFormIssueMicMeta()?.map(
                                              (singleMicSection, index) => (
                                                  <Col
                                                      xs={24}
                                                      md={24}
                                                      className="gx-pl-0"
                                                      key={singleMicSection.key}
                                                  >
                                                      {singleMicSection.title !=
                                                          '' && (
                                                          <h3 className="gx-mt-3">
                                                              {singleMicSection.required && (
                                                                  <span
                                                                      style={{
                                                                          color: 'red',
                                                                      }}
                                                                  >
                                                                      {' '}
                                                                      *{' '}
                                                                  </span>
                                                              )}
                                                              {
                                                                  singleMicSection.title
                                                              }
                                                              <hr className="gx-bg-dark"></hr>
                                                          </h3>
                                                      )}
                                                      <Form.Item
                                                          name={
                                                              'mic_recording_uploads'
                                                          }
                                                      >
                                                          <MicInputV2
                                                              // className="gx-w-50"
                                                              // demoMode
                                                              required={
                                                                  singleMicSection.required
                                                              }
                                                              maxColSpan={6}
                                                              authToken={http_utils.getAuthToken()}
                                                              prefixDomain={http_utils.getCDNDomain()}
                                                              onFilesChanged={(
                                                                  files
                                                              ) => {
                                                                  onMicFilesChanged(
                                                                      singleMicSection.key,
                                                                      files
                                                                  );
                                                              }}
                                                              onReadyStatusChanged={(
                                                                  isReady
                                                              ) => {
                                                                  onMicFileUploaderReadyChange(
                                                                      singleMicSection.key,
                                                                      isReady
                                                                  );
                                                              }}
                                                              initialFiles={
                                                                  prefillFormData
                                                                      .mic_files?.[
                                                                      singleMicSection
                                                                          .key
                                                                  ] || []
                                                              }
                                                          />
                                                      </Form.Item>
                                                  </Col>
                                              )
                                          )}
                                      </Row>
                                      <hr />
                                  </>
                              ),
                          },
                      ]
                    : []),
                ...(trackLineItemWiseProgress &&
                showLineItemBySelection &&
                canCurrentUserEdit
                    ? [
                          {
                              key: 'selected_line_items',
                              label: 'Line Item Progress',
                              widget: 'select',
                              widgetProps: {
                                  mode: 'multiple',
                                  maxTagCount: isMobileView() ? 1 : 3,
                                  // maxTagTextLength: 20,
                                  showSearch: true,
                                  optionFilterProp: 'children',
                                  placeholder:
                                      'Select line items to track progress',
                                  style: { width: '100%' },
                                  allowClear: true,
                              },
                              options: getLineItemOptions(),
                              onChange: (value) => {
                                  forceUpdate();
                              },
                          },
                      ]
                    : []),
                ...(showLineItemBySelection &&
                trackLineItemWiseProgress &&
                selectedLineItemsFrProgress &&
                selectedLineItemsFrProgress.length > 0
                    ? [
                          {
                              render: () => (
                                  <div className="gx-module-box-content gx-px-3 gx-py-3 wy-show-line-items-by-selection">
                                      <FormBuilder
                                          meta={{
                                              fields: getSelectedLineItemWiseProgressMeta(
                                                  form.getFieldValue(
                                                      'selected_line_items'
                                                  )
                                              ),
                                          }}
                                          form={form}
                                      />
                                  </div>
                              ),
                          },
                      ]
                    : []),
                ...(trackLineItemWiseProgress &&
                (!showLineItemBySelection || !canCurrentUserEdit)
                    ? getLineItemWiseProgressMeta()
                    : []),

                ...(trackLineItemWisePhotos
                    ? [
                          {
                              render: () => (
                                  <>
                                      <Row>
                                          {getLineItemWiseProgressMeta()?.map(
                                              (singleFileSection, index) => {
                                                  handleFileSectionRequiredStatus(
                                                      singleFileSection
                                                  );
                                                  return (
                                                      <Col
                                                          xs={24}
                                                          md={24}
                                                          className="gx-pl-0"
                                                          key={
                                                              singleFileSection.key
                                                          }
                                                      >
                                                          {singleFileSection.title !=
                                                              '' && (
                                                              <h3 className="gx-mt-3">
                                                                  {singleFileSection.required && (
                                                                      <span
                                                                          style={{
                                                                              color: 'red',
                                                                          }}
                                                                      >
                                                                          {' '}
                                                                          *{' '}
                                                                      </span>
                                                                  )}
                                                                  {
                                                                      singleFileSection.title
                                                                  }
                                                                  <hr className="gx-bg-dark"></hr>
                                                              </h3>
                                                          )}
                                                          <Form.Item
                                                              name={
                                                                  'file_uploads'
                                                              }
                                                          >
                                                              {
                                                                  <>
                                                                      <Button
                                                                          type="link"
                                                                          onClick={() => {
                                                                              setShowUploadedFilesModal(
                                                                                  singleFileSection.key
                                                                              );
                                                                              selectFromUploadedFilesClick(
                                                                                  sbtskDetailViewData
                                                                              );
                                                                          }}
                                                                      >
                                                                          Select
                                                                          from
                                                                          uploaded
                                                                          files
                                                                      </Button>

                                                                      <Modal
                                                                          title="Attachments"
                                                                          centered
                                                                          visible={
                                                                              singleFileSection.key ==
                                                                              showUploadedFilesModal
                                                                          }
                                                                          onOk={() => {
                                                                              handleOkFrUploadedFilesModal(
                                                                                  prefillFormData
                                                                                      .attachments?.[
                                                                                      singleFileSection
                                                                                          .key
                                                                                  ] ||
                                                                                      [],
                                                                                  singleFileSection.key
                                                                              );
                                                                          }}
                                                                          onCancel={() =>
                                                                              setShowUploadedFilesModal(
                                                                                  ''
                                                                              )
                                                                          }
                                                                          width={
                                                                              1000
                                                                          }
                                                                      >
                                                                          {loadingSbtskDetailViewData ? (
                                                                              <div className="gx-loader-view gx-loader-position">
                                                                                  <CircularProgress />
                                                                              </div>
                                                                          ) : sbtskDetailViewData ==
                                                                            undefined ? (
                                                                              <p className="gx-text-red">
                                                                                  {
                                                                                      errorFrloadingSbtskDetailViewData
                                                                                  }
                                                                              </p>
                                                                          ) : (
                                                                              <AttachmentsPreview
                                                                                  attachments={getAllMergedAttachments(
                                                                                      sbtskDetailViewData,
                                                                                      prefillFormData
                                                                                          .attachments?.[
                                                                                          singleFileSection
                                                                                              .key
                                                                                      ] ||
                                                                                          []
                                                                                  )}
                                                                                  noteSelectedFilesBySection={(
                                                                                      files
                                                                                  ) => {
                                                                                      noteSelectedFilesBySection(
                                                                                          singleFileSection.key,
                                                                                          files
                                                                                      );
                                                                                  }}
                                                                              />
                                                                          )}

                                                                          {/* todo */}
                                                                      </Modal>
                                                                      {
                                                                          selectedImage[
                                                                              singleFileSection
                                                                                  .key
                                                                          ] && (
                                                                              <div
                                                                                  key={
                                                                                      index
                                                                                  }
                                                                              >
                                                                                  <AttachmentsPreview
                                                                                      attachments={
                                                                                          selectedImage[
                                                                                              singleFileSection
                                                                                                  .key
                                                                                          ]
                                                                                      }
                                                                                      noteSelectedFilesBySection={(
                                                                                          files
                                                                                      ) => {
                                                                                          noteSelectedFilesBySection(
                                                                                              singleFileSection.key,
                                                                                              files
                                                                                          );
                                                                                      }}
                                                                                      selectOnly={
                                                                                          false
                                                                                      }
                                                                                      sectionKey={
                                                                                          singleFileSection.key
                                                                                      }
                                                                                      prevUploadedFiles={
                                                                                          prefillFormData
                                                                                              ?.attachments?.[
                                                                                              singleFileSection
                                                                                                  .key
                                                                                          ]
                                                                                      }
                                                                                  />
                                                                              </div>
                                                                          )
                                                                          // ))
                                                                      }
                                                                  </>
                                                              }
                                                              <S3Uploader
                                                                  // className="gx-w-50"
                                                                  // demoMode
                                                                  required={
                                                                      singleFileSection.required
                                                                  }
                                                                  maxColSpan={4}
                                                                  authToken={http_utils.getAuthToken()}
                                                                  prefixDomain={http_utils.getCDNDomain()}
                                                                  customFilePrefixName={getCustomPrefixNameFrUploadingFiles()}
                                                                  totalFiles={
                                                                      prefillFormData
                                                                          ?.attachments?.[
                                                                          singleFileSection
                                                                              .key
                                                                      ]
                                                                  }
                                                                  onFilesChanged={(
                                                                      files,
                                                                      deletedFileUrl
                                                                  ) => {
                                                                      onFilesChanged(
                                                                          singleFileSection.key,
                                                                          files,
                                                                          deletedFileUrl
                                                                      );
                                                                  }}
                                                                  onReadyStatusChanged={(
                                                                      isReady
                                                                  ) => {
                                                                      onFileUploaderReadyChange(
                                                                          singleFileSection.key,
                                                                          isReady
                                                                      );
                                                                  }}
                                                                  initialFiles={
                                                                      prefillFormData
                                                                          .attachments?.[
                                                                          singleFileSection
                                                                              .key
                                                                      ] || []
                                                                  }
                                                                  customPreviewHeight="100%"
                                                                  customFileIconMaxWidth="40px"
                                                                  compConfig={{
                                                                      name: 'daily-request-status-editor-sp',
                                                                  }}
                                                              />
                                                          </Form.Item>
                                                      </Col>
                                                  );
                                              }
                                          )}
                                      </Row>
                                      <hr />
                                  </>
                              ),
                          },
                      ]
                    : []),
                ...dailyUpdateFormMeta(),
                {
                    render: () => (
                        <>
                            <Row>
                                {getFormFileMeta()?.map(
                                    (singleFileSection, index) => {
                                        handleFileSectionRequiredStatus(
                                            singleFileSection
                                        );
                                        return (
                                            <Col
                                                xs={24}
                                                md={24}
                                                className="gx-pl-0"
                                                key={singleFileSection.key}
                                            >
                                                {singleFileSection.title !=
                                                    '' && (
                                                    <h3 className="gx-mt-3">
                                                        {singleFileSection.required && (
                                                            <span
                                                                style={{
                                                                    color: 'red',
                                                                }}
                                                            >
                                                                {' '}
                                                                *{' '}
                                                            </span>
                                                        )}
                                                        {
                                                            singleFileSection.title
                                                        }
                                                        <hr className="gx-bg-dark"></hr>
                                                    </h3>
                                                )}
                                                <Form.Item
                                                    name={'file_uploads'}
                                                >
                                                    {
                                                        <>
                                                            <Button
                                                                type="link"
                                                                onClick={() => {
                                                                    setShowUploadedFilesModal(
                                                                        singleFileSection.key
                                                                    );
                                                                    selectFromUploadedFilesClick(
                                                                        sbtskDetailViewData
                                                                    );
                                                                }}
                                                            >
                                                                Select from
                                                                uploaded files
                                                            </Button>

                                                            <Modal
                                                                title="Attachments"
                                                                centered
                                                                visible={
                                                                    singleFileSection.key ==
                                                                    showUploadedFilesModal
                                                                }
                                                                onOk={() => {
                                                                    handleOkFrUploadedFilesModal(
                                                                        prefillFormData
                                                                            .attachments?.[
                                                                            singleFileSection
                                                                                .key
                                                                        ] || [],
                                                                        singleFileSection.key
                                                                    );
                                                                }}
                                                                onCancel={() =>
                                                                    setShowUploadedFilesModal(
                                                                        ''
                                                                    )
                                                                }
                                                                width={1000}
                                                            >
                                                                {loadingSbtskDetailViewData ? (
                                                                    <div className="gx-loader-view gx-loader-position">
                                                                        <CircularProgress />
                                                                    </div>
                                                                ) : sbtskDetailViewData ==
                                                                  undefined ? (
                                                                    <p className="gx-text-red">
                                                                        {
                                                                            errorFrloadingSbtskDetailViewData
                                                                        }
                                                                    </p>
                                                                ) : (
                                                                    <AttachmentsPreview
                                                                        attachments={getAllMergedAttachments(
                                                                            sbtskDetailViewData,
                                                                            prefillFormData
                                                                                .attachments?.[
                                                                                singleFileSection
                                                                                    .key
                                                                            ] ||
                                                                                []
                                                                        )}
                                                                        noteSelectedFilesBySection={(
                                                                            files
                                                                        ) => {
                                                                            noteSelectedFilesBySection(
                                                                                singleFileSection.key,
                                                                                files
                                                                            );
                                                                        }}
                                                                    />
                                                                )}
                                                            </Modal>
                                                            {
                                                                selectedImage[
                                                                    singleFileSection
                                                                        .key
                                                                ] && (
                                                                    <div
                                                                        key={
                                                                            index
                                                                        }
                                                                    >
                                                                        <AttachmentsPreview
                                                                            attachments={
                                                                                selectedImage[
                                                                                    singleFileSection
                                                                                        .key
                                                                                ]
                                                                            }
                                                                            noteSelectedFilesBySection={(
                                                                                files
                                                                            ) => {
                                                                                noteSelectedFilesBySection(
                                                                                    singleFileSection.key,
                                                                                    files
                                                                                );
                                                                            }}
                                                                            selectOnly={
                                                                                false
                                                                            }
                                                                            sectionKey={
                                                                                singleFileSection.key
                                                                            }
                                                                            prevUploadedFiles={
                                                                                prefillFormData
                                                                                    ?.attachments?.[
                                                                                    singleFileSection
                                                                                        .key
                                                                                ]
                                                                            }
                                                                        />
                                                                    </div>
                                                                )
                                                                // ))
                                                            }
                                                        </>
                                                    }

                                                    <S3Uploader
                                                        // className="gx-w-50"
                                                        // demoMode
                                                        required={
                                                            singleFileSection.required
                                                        }
                                                        maxColSpan={4}
                                                        authToken={http_utils.getAuthToken()}
                                                        prefixDomain={http_utils.getCDNDomain()}
                                                        customFilePrefixName={getCustomPrefixNameFrUploadingFiles()}
                                                        totalFiles={
                                                            prefillFormData
                                                                ?.attachments?.[
                                                                singleFileSection
                                                                    .key
                                                            ]
                                                        }
                                                        onFilesChanged={(
                                                            files,
                                                            deletedFileUrl
                                                        ) => {
                                                            onFilesChanged(
                                                                singleFileSection.key,
                                                                files,
                                                                deletedFileUrl
                                                            );
                                                        }}
                                                        onReadyStatusChanged={(
                                                            isReady
                                                        ) => {
                                                            onFileUploaderReadyChange(
                                                                singleFileSection.key,
                                                                isReady
                                                            );
                                                        }}
                                                        initialFiles={
                                                            prefillFormData
                                                                .attachments?.[
                                                                singleFileSection
                                                                    .key
                                                            ] || []
                                                        }
                                                        customPreviewHeight="100%"
                                                        customFileIconMaxWidth="40px"
                                                        compConfig={{
                                                            name: 'daily-request-status-editor',
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                        );
                                    }
                                )}
                                {getFormMicMeta()?.map(
                                    (singleMicSection, index) => {
                                        return (
                                            <Col
                                                xs={24}
                                                md={24}
                                                className="gx-pl-0"
                                                key={singleMicSection.key}
                                            >
                                                {singleMicSection.title !=
                                                    '' && (
                                                    <h3 className="gx-mt-3">
                                                        {singleMicSection.required && (
                                                            <span
                                                                style={{
                                                                    color: 'red',
                                                                }}
                                                            >
                                                                {' '}
                                                                *{' '}
                                                            </span>
                                                        )}
                                                        {singleMicSection.title}
                                                        <hr className="gx-bg-dark"></hr>
                                                    </h3>
                                                )}
                                                <Form.Item
                                                    name={
                                                        'mic_recording_uploads'
                                                    }
                                                >
                                                    <MicInputV2
                                                        // className="gx-w-50"
                                                        // demoMode
                                                        required={
                                                            singleMicSection.required
                                                        }
                                                        maxColSpan={6}
                                                        authToken={http_utils.getAuthToken()}
                                                        prefixDomain={http_utils.getCDNDomain()}
                                                        onFilesChanged={(
                                                            files
                                                        ) => {
                                                            onMicFilesChanged(
                                                                singleMicSection.key,
                                                                files
                                                            );
                                                        }}
                                                        onReadyStatusChanged={(
                                                            isReady
                                                        ) => {
                                                            onMicFileUploaderReadyChange(
                                                                singleMicSection.key,
                                                                isReady
                                                            );
                                                        }}
                                                        initialFiles={
                                                            prefillFormData
                                                                .mic_files?.[
                                                                singleMicSection
                                                                    .key
                                                            ] || []
                                                        }
                                                    />
                                                </Form.Item>
                                            </Col>
                                        );
                                    }
                                )}
                            </Row>
                            <hr />
                        </>
                    ),
                },
                {
                    key: 'day_progress',
                    label: 'Day progress',
                    widget: SliderWidget,
                    widgetProps: {
                        tooltip: { formatter: formatter },
                        onChange: (value) => {
                            if (
                                srvcConfigData.daily_progress_update_mode ==
                                'strict_mode'
                            ) {
                                checkFrProgressionAcrossRange(
                                    value,
                                    day,
                                    'day_progress'
                                );
                            }
                        },
                        disabled: trackLineItemWiseProgress,
                    },
                },
            ],
        };
    };

    const onFormValueChange = () => {
        setIsFormChanged(true);
    };

    /**
     * Extracts and maps the progress values for all line items from the form data.
     *
     * This function determines whether the service provider tab is active and selects the appropriate
     * line items (`sp_line_items` or `line_items`) from the request data. It uses the service configuration
     * (`srvc_type_line_item_config`) to iterate through the defined line item groups, and fetches progress
     * values for each item using their `input_table_id` as a unique key.
     *
     * @returns {Object} A map of progress field keys (`progress_<input_table_id>`) to their corresponding values
     *                   from the form. If the form value is not set, the value will be `null`.
     *
     * @example
     * {
     *   progress_1234: 50,
     *   progress_5678: 75
     * }
     */
    const getAllLineItemProgressWithValues = () => {
        let progressMap = {};
        let reqLineItemsFormData = isSrvcPrvdrTab
            ? reqData?.form_data?.sp_line_items?.form_data
            : reqData?.form_data?.line_items?.form_data;

        if (!reqLineItemsFormData) return progressMap;

        let lineItemConfig = JSON.parse(
            srvcConfigData?.srvc_type_line_item_config
        );

        Object.keys(lineItemConfig).forEach((lineItemGrpKey) => {
            let itemsInGroup = reqLineItemsFormData[lineItemGrpKey] || [];
            itemsInGroup.forEach((item) => {
                const key = `progress_${item.input_table_id}`;
                const value = form.getFieldValue(key) ?? null; // you can default to 0 if needed
                progressMap[key] = value;
            });
        });

        return progressMap;
    };

    const onSubmit = (values) => {
        setIsFormSubmitting(true);
        setError(undefined);
        let existingDayStatusUpdates = existingDailyStatusObj[day];
        if (existingDayStatusUpdates == undefined) {
            existingDayStatusUpdates = {};
        }
        existingDayStatusUpdates.update_day_and_time =
            getCurrentDateAndTimeFrDisplay();
        let existingFileOrSelectedNewFile = {
            ...existingDayStatusUpdates?.attachments,
            ...filesBySection,
        };
        let existingMicRecordingsOrSelectedNewRecordings = {
            ...existingDayStatusUpdates?.mic_files,
            ...micRecordingsBySection,
        };
        values['attachments'] = existingFileOrSelectedNewFile;
        values['mic_files'] = existingMicRecordingsOrSelectedNewRecordings;

        // save all the line items progress data if selected_line_items config enabled
        if (
            form.getFieldValue('selected_line_items') &&
            srvcConfigData?.daily_update_track_line_item_progress == 'Yes'
        ) {
            const allLineItemProgressMap = getAllLineItemProgressWithValues();

            Object.entries(allLineItemProgressMap).forEach(([key, value]) => {
                if (!(key in values)) {
                    values[key] = value ?? 0; // Use existing value, or 0 if null/undefined
                }
            });
        }

        let mergedStatusUpdateFrDay = {
            ...existingDayStatusUpdates,
            ...values, // newDayStatusUpdateValues
        };
        existingDailyStatusObj[day] = mergedStatusUpdateFrDay;

        let daily_status_updates_key_name = ConfigHelpers.isServiceProvider()
            ? 'sp_daily_status_updates'
            : 'daily_status_updates';
        var params = {
            [daily_status_updates_key_name]: {
                ...existingDailyStatusObj,
            },
            updatedDay: day,
        };
        // console.log('params',params)
        const onComplete = (resp) => {
            setIsFormSubmitting(false);
            setError(undefined);
            if (onChange) {
                onChange(day);
            }
            // message.info(JSON.stringify(resp.data));
        };
        const onError = (error) => {
            setIsFormSubmitting(false);
            setError(http_utils.decodeErrorToMessage(error));
        };
        var url = urlToSubmitFrUpdates;
        http_utils.performPutCall(url, params, onComplete, onError);
    };

    const getIssueFileMeta = () => {
        let dailyUpdateIssueFileMeta = [];
        dailyUpdateIssueFileMeta = decodeFileSectionsFrmJson(
            srvcConfigData?.daily_update_issue_form_fields
        );
        return dailyUpdateIssueFileMeta;
    };

    const getFormIssueMicMeta = () => {
        let dailyUpdateIssueMicMeta = [];
        dailyUpdateIssueMicMeta = decodeMicSectionsFrmJson(
            srvcConfigData?.daily_update_issue_form_fields
        );
        return dailyUpdateIssueMicMeta;
    };

    const getFormFileMeta = () => {
        let dailyUpdateFileMeta = [];
        dailyUpdateFileMeta = isSrvcPrvdrTab
            ? decodeFileSectionsFrmJson(
                  srvcConfigData?.sp_daily_update_form_fields
              )
            : decodeFileSectionsFrmJson(
                  srvcConfigData?.daily_update_form_fields
              );
        return dailyUpdateFileMeta;
    };

    const getFormMicMeta = () => {
        let dailyUpdateMicMeta = [];
        dailyUpdateMicMeta = isSrvcPrvdrTab
            ? decodeMicSectionsFrmJson(
                  srvcConfigData?.sp_daily_update_form_fields
              )
            : decodeMicSectionsFrmJson(
                  srvcConfigData?.daily_update_form_fields
              );
        return dailyUpdateMicMeta;
    };

    const onFilesChanged = (section, files, deletedFileUrl = undefined) => {
        onFilesChangedFn(
            section,
            files,
            filesBySection,
            setFilesBySection,
            onFormValueChange,
            deletedFileUrl
        );
    };

    const onMicFilesChanged = (section, files) => {
        let newFilesBySection = _.cloneDeep(micRecordingsBySection);
        let newFiles = [];

        if (
            micRecordingsBySection[section] &&
            files?.length >= micRecordingsBySection[section]?.length
        ) {
            newFiles = files.filter(
                (singleFile) =>
                    !micRecordingsBySection[section].includes(singleFile)
            );
        } else {
            newFiles = files;
        }
        newFilesBySection[section] =
            micRecordingsBySection[section] &&
            files.length >= micRecordingsBySection[section].length
                ? [...micRecordingsBySection[section], ...newFiles]
                : files;
        //    console.log('newfile',newFilesBySection)
        setMicRecordingsBySection(newFilesBySection);
        onFormValueChange();
    };

    const onFileUploaderReadyChange = (section, isReady) => {
        let newSectionWiseReady = sectionWiseUploaderReady;
        newSectionWiseReady[section] = isReady;
        setSectionWiseUploaderReady(newSectionWiseReady);
        setAllFileUploadersReady(getAllFileUploadersReady());
    };

    const onMicFileUploaderReadyChange = (section, isReady) => {
        let newSectionWiseReady = sectionWiseMicUploaderReady;
        newSectionWiseReady[section] = isReady;
        setSectionWiseMicUploaderReady(newSectionWiseReady);
        setAllMicRecordingsUploadersReady(getAllMicRecordingsUploadersReady());
    };

    const getAllFileUploadersReady = () => {
        let notReady = false;
        Object.keys(sectionWiseUploaderReady).map((section) => {
            if (!sectionWiseUploaderReady[section]) {
                notReady = true;
            }
        });
        return !notReady;
    };

    const getAllMicRecordingsUploadersReady = () => {
        let notReady = false;
        Object.keys(sectionWiseMicUploaderReady).map((section) => {
            if (!sectionWiseMicUploaderReady[section]) {
                notReady = true;
            }
        });
        return !notReady;
    };

    let prefillFormData =
        convertDateFieldsToMoments(
            existingDailyStatusObj[day],
            meta().fields
        ) || {};
    // TODO convertdatefields to moment
    //set no remarks by default
    const remark = form?.getFieldValue('day_remarks');
    if (prefillFormData?.day_remarks == undefined && remark == undefined) {
        form.setFieldsValue({
            day_remarks: 'No remarks',
        });
    }

    const getMeta = (fields, prefillFormData) => {
        if (!canCurrentUserEdit) {
            const viewMeta = getViewModeMetaFrFormMeta(fields, prefillFormData);
            if (viewMeta.length > 0) {
                return viewMeta;
            } else {
                return [{ render: () => <div>No updates added</div> }];
            }
        } else {
            return meta();
        }
    };

    // if line item deleted after updating daily update form then delete that line item id from daily update form (line item progress selection)
    const updatedSelectedLineItems =
        prefillFormData?.selected_line_items?.filter((singleLineItem) => {
            return getLineItemOptions().some(
                (option) => option.value === singleLineItem
            );
        });

    prefillFormData.selected_line_items = updatedSelectedLineItems;

    return (
        <div className="gx-mt-3">
            <Form
                initialValues={prefillFormData}
                form={form}
                layout="vertical"
                onFinish={onSubmit}
                onValuesChange={onFormValueChange}
            >
                <FormBuilder
                    meta={getMeta(meta().fields, prefillFormData)}
                    form={form}
                    viewMode={!canCurrentUserEdit}
                />

                <div className="gx-mt-0">
                    {(!allFileUploadersReady ||
                        !allMicRecordingsUploadersReady) && <Spin></Spin>}
                    {canCurrentUserEdit && (
                        <Button
                            type="primary"
                            htmlType="submit"
                            disabled={
                                isFormSubmitting ||
                                !allFileUploadersReady ||
                                !isFormChanged ||
                                !allMicRecordingsUploadersReady
                            }
                        >
                            Save
                        </Button>
                    )}
                    {isFormSubmitting ? (
                        <div className="gx-loader-view gx-loader-position">
                            <CircularProgress />
                        </div>
                    ) : null}
                    {error ? <p className="gx-text-red">{error}</p> : null}
                </div>
            </Form>
            {/* {JSON.stringify(editorDayDetails)} */}
        </div>
    );
};

const onFilesChangedFn = (
    section,
    files,
    filesBySection,
    setFilesBySection,
    onFormValueChange,
    deletedFileUrl
) => {
    let newFilesBySection = _.cloneDeep(filesBySection);
    let newFiles = [];

    if (
        filesBySection[section] &&
        files?.length >= filesBySection[section]?.length
    ) {
        newFiles = files.filter(
            (singleFile) => !filesBySection[section].includes(singleFile)
        );
    } else {
        if (filesBySection[section]) {
            newFiles = [...new Set(files.concat(filesBySection[section]))];
        } else {
            newFiles = files;
        }
    }

    // Get the existing files for the specified section from the filesBySection object,
    // or initialize an empty array if no files exist for the section.
    const existingFiles = filesBySection[section] || [];

    // Combine the current files (passed as an argument) with the existing files,
    // and use a Set to ensure unique values in the resulting array.
    // Create a new Set with deep-copied objects
    const combinedFiles = [...new Set([...files, ...existingFiles])];

    // Check if there are existing files for the section and if the number of files
    // passed as an argument is greater than or equal to the number of existing files.
    // If true, update the files for the section with the combined unique files;
    // otherwise, use the files passed as an argument.
    newFilesBySection[section] =
        filesBySection[section] && files.length >= existingFiles.length
            ? combinedFiles
            : files;

    // If deletedFileUrl exits then remove from combinedFiles
    if (deletedFileUrl) {
        newFilesBySection[section] = combinedFiles?.filter(
            (singleFile) => singleFile !== deletedFileUrl
        );
    }
    setFilesBySection(newFilesBySection);
    onFormValueChange();
};

// Export functions and components
export { onFilesChangedFn };
export default DailyReqStatusEditor;
