import { notification, Spin, message } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';
import MetaInputTable from '../../../../components/wify-utils/MetaInputTable';
import * as XLSX from 'xlsx';
import { isInsideMobileApp } from '../../../../util/AppHelpers';
import { downloadExcelFileForAndroid } from '../../../../util/helpers';
import CountUp from '../../../../components/wify-utils/CountUp/countUp';

const numOr0 = (n) => (!n || isNaN(n) ? 0 : n);
const FinalBillingTable = ({
    srvcConfigData,
    rowData,
    onChange,
    readOnly,
    isServiceProviderTab,
}) => {
    const [reRenderLineItems, setReRenderLineItems] = useState(false);
    const [renderHelper, setRenderHelper] = useState(false);
    const [error, setError] = useState(undefined);

    useEffect(() => {
        setRenderHelper(!renderHelper);
    }, [rowData]);

    const isEnableBillingDiscount = () => {
        return srvcConfigData?.srvc_type_enable_billing_discounting;
    };

    const discountValidationNotification = (type, message) => {
        notification[type]({
            message: 'Invalid value',
            description: message,
            duration: 5,
        });
    };

    const getColMeta = () => {
        let { qtyDiscount, rateDiscount, subTotalDiscount } =
            getBillingDisColumn();
        const staticColumns = [
            {
                key: 'type',
                label: 'Type',
                viewMode: true,
                // render: () => <a>action</a>
            },
            {
                key: 'item',
                label: 'Item',
                viewMode: true,
            },
            {
                key: 'unit',
                label: 'Unit',
                viewMode: true,
            },
            {
                key: 'qty',
                label: 'Qty',
                widget: 'number',
                viewMode: true,
                renderView: (text) => {
                    return (
                        <div className="gx-px-2 gx-text-orange">
                            <CountUp
                                end={text}
                                duration={0.5}
                                decimal="."
                                decimals={2}
                            />
                        </div>
                    );
                },
            },
            ...(qtyDiscount || []),
            {
                key: 'rate',
                label: 'Rate',
                viewMode: true,
                renderView: (text) => {
                    return (
                        <div className="gx-px-2 gx-text-orange">
                            <CountUp
                                end={text}
                                duration={0.5}
                                decimal="."
                                decimals={2}
                            />
                        </div>
                    );
                },
            },
            ...(rateDiscount || []),
            {
                key: 'sub_total',
                label: 'Sub Total',
                viewMode: true,
                renderView: (text) => {
                    return (
                        <div className="gx-px-2 gx-text-orange">
                            <CountUp
                                end={text}
                                duration={0.5}
                                decimal="."
                                decimals={2}
                            />
                        </div>
                    );
                },
            },
            ...(subTotalDiscount || []),
        ];
        return staticColumns;
    };

    const getRowMeta = () => {
        let tableData = [];
        rowData.forEach((singlesrvcReqCalenderData) => {
            let obj = {
                type: singlesrvcReqCalenderData.type,
                item: singlesrvcReqCalenderData.item,
                unit: singlesrvcReqCalenderData.unit,
                qty: singlesrvcReqCalenderData.qty,
                rate: singlesrvcReqCalenderData.rate,
                sub_total: singlesrvcReqCalenderData.sub_total,
                qty_discount: singlesrvcReqCalenderData.qty_discount,
                qty_after_discount:
                    singlesrvcReqCalenderData.qty_after_discount,
                rate_discount: singlesrvcReqCalenderData.rate_discount,
                rate_after_discount:
                    singlesrvcReqCalenderData.rate_after_discount,
                discount_rs: singlesrvcReqCalenderData.discount_rs,
                sub_total_after_discount:
                    singlesrvcReqCalenderData.sub_total_after_discount,
            };
            tableData.push(obj);
        });
        return tableData;
    };

    const getBillingDisColumn = () => {
        let finalDisColumns = {};
        if (isEnableBillingDiscount()) {
            let qtyDiscount = [
                {
                    key: 'qty_discount',
                    label: 'Qty (Discount)',
                    widget: 'number',
                },
                {
                    key: 'qty_after_discount',
                    label: 'Qty (After discount)',
                    viewMode: true,
                },
            ];
            finalDisColumns['qtyDiscount'] = qtyDiscount;

            let rateDiscount = [
                {
                    key: 'rate_discount',
                    label: 'Rate (Discount)',
                    widget: 'number',
                },
                {
                    key: 'rate_after_discount',
                    label: 'Rate (After discount)',
                    viewMode: true,
                },
            ];
            finalDisColumns['rateDiscount'] = rateDiscount;

            let subTotalDiscount = [
                {
                    key: 'discount_rs',
                    label: 'Discount (Rs.)',
                    viewMode: true,
                },
                {
                    key: 'sub_total_after_discount',
                    label: 'Sub total (After discount)',
                    viewMode: true,
                    renderView: (text) => {
                        return (
                            <div className="gx-px-2 gx-text-orange">
                                <CountUp
                                    end={text}
                                    duration={0.5}
                                    decimal="."
                                    decimals={2}
                                />
                            </div>
                        );
                    },
                },
            ];
            finalDisColumns['subTotalDiscount'] = subTotalDiscount;
        }
        return finalDisColumns;
    };

    const setNewRowData = (newData) => {
        let finalFormData = {};
        newData.forEach((singleLineItem) => {
            if (
                numOr0(singleLineItem.qty_discount) > numOr0(singleLineItem.qty)
            ) {
                discountValidationNotification(
                    'warning',
                    'Please enter lower discount qty'
                );
                return;
            }
            if (
                numOr0(singleLineItem.rate_discount) >
                numOr0(singleLineItem.rate)
            ) {
                discountValidationNotification(
                    'warning',
                    'Please enter lower discount rate'
                );
                return;
            }
            finalFormData[singleLineItem.input_table_id] = {
                qty_discount: singleLineItem.qty_discount,
                rate_discount: singleLineItem.rate_discount,
            };
        });
        let newLineItemsData = {};
        newLineItemsData['form_data'] = finalFormData;
        // newLineItemsData['total'] = getLineItemsTotal(newData);// For history
        onChange(newLineItemsData);
    };

    if (reRenderLineItems) {
        setTimeout(() => setReRenderLineItems(false), 100);
    }

    const downloadLineItem = () => {
        let ColMeta = getColMeta();
        let RowMeta = getRowMeta();
        let lineItem = [];

        RowMeta.forEach((singleRowMeta) => {
            let dummyObj = {};
            ColMeta.forEach((singleColumns) => {
                // Use the 'label' property from ColMeta as the key
                const key = singleColumns.label;
                // Assign the corresponding value from RowMeta to the key
                dummyObj[key] = singleRowMeta[singleColumns.key] || '';
            });
            lineItem.push(dummyObj);
        });

        try {
            let wb = XLSX.utils.book_new();
            let ws = XLSX.utils.json_to_sheet(lineItem);
            XLSX.utils.book_append_sheet(wb, ws, 'sheet1');
            if (!isInsideMobileApp()) {
                XLSX.writeFile(wb, 'LineItems.xlsx');
                message.success('Downloaded');
            } else {
                // Create an object to store the workbook and sheet
                downloadExcelFileForAndroid(wb, 'LineItems');
            }
        } catch (error) {
            setError(error);
        }
    };

    return (
        <>
            <div>
                {!reRenderLineItems && (
                    <Fragment>
                        <div className="wy-flex-row-between">
                            <p>
                                <b>Final billing details</b>
                            </p>
                            {
                                //isServiceProviderTab &&
                                //srvcConfigData?.vertical_nature == "project_based" &&
                                rowData.length > 0 && (
                                    <button
                                        className="btn btn-primary gx-w-fit-content"
                                        onClick={(e) => {
                                            e.preventDefault(); // Prevents the default behavior
                                            downloadLineItem();
                                        }}
                                    >
                                        Download Line Items
                                    </button>
                                )
                            }
                        </div>
                        <MetaInputTable
                            edittable
                            colMeta={getColMeta()}
                            rowData={rowData}
                            noFilters
                            onChange={(newData) => {
                                setNewRowData(newData);
                            }}
                            readOnly={readOnly}
                            hideActionBtn
                        />
                    </Fragment>
                )}
            </div>
        </>
    );
};

export default FinalBillingTable;
