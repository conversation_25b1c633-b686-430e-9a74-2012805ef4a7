import React, { Component, createRef } from 'react';
import {
    Collapse,
    Form,
    Button,
    Select,
    Row,
    Col,
    Tag,
    notification,
    Tooltip,
    Badge,
    Popconfirm,
} from 'antd';
import CircularProgress from '../../components/CircularProgress';
import http_utils from '../../util/http_utils';
import ConfigHelpers from '../../util/ConfigHelpers';
import StarRatingCompact from '../../components/WIFY/WifyComponents/StarRatingCompact';
import { size } from 'lodash';

const { Option } = Select;
const { Panel } = Collapse;

const mandatoryFieldStatusNotification = (type, mandatory_statuses_labels) => {
    notification[type]({
        message: 'Mandatory fields missing',
        description: mandatory_statuses_labels.join(', '),
        duration: 10,
        //   placement : 'top',
    });
};
class StatusChanger extends Component {
    constructor(props) {
        super(props);
        this.formRef = createRef();
        this.state = {
            activeFilters: {},
            drawerState: false,
            showItemEditor: false,
            isLoadingViewData: false,
            viewData: undefined,
            error: '',
            currentStatus: this.getStatusByKey(this.props.currentStatus),
            isFormSubmitting: false,
        };
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.currentStatus != this.props.currentStatus) {
            this.setState({
                currentStatus: this.getStatusByKey(this.props.currentStatus),
            });
        }
    }

    componentDidMount() {
        // console.log('Rxd props in status changer',this.props);
    }

    submitForm = (data) => {
        this.setState({
            isFormSubmitting: true,
        });

        var params = data;
        if (data.new_status == this.state.currentStatus.value) {
            // No change
            this.setState({
                isFormSubmitting: false,
            });
            return;
        }
        params['host_d'] = window.location.host;
        params['is_frm_frontend'] = true;
        const onComplete = (resp) => {
            this.setState({
                isFormSubmitting: false,
                error: '',
                currentStatus: this.getStatusByKey(params.new_status),
            });
            this.tellParentToRefreshList(resp.entry_id);
        };
        const onError = (error) => {
            // compare statuses here
            let status_code = http_utils.decodeErrorGetStatusCode(error);
            let errorMsg = '';
            if (status_code == 400) {
                if (error?.response?.data) {
                    errorMsg = mandatoryFieldStatusNotification(
                        'error',
                        error?.response?.data
                    );
                }
            } else {
                errorMsg = http_utils.decodeErrorToMessage(error);
            }
            this.setState({
                isFormSubmitting: false,
                error: errorMsg,
            });
        };
        http_utils.performPutCall(
            this.props.urlToSubmit,
            params,
            onComplete,
            onError
        );
    };

    tellParentToRefreshList(entry_id) {
        // console.log("Trying to to tell parent to refresh list");
        if (this.props.onDataModified != undefined) {
            this.props.onDataModified(entry_id);
        }
    }

    getStatusByKey(key) {
        let returnStatus;
        this.props.possibleStatus.forEach((singleStatus) => {
            if (singleStatus.value == key) {
                returnStatus = singleStatus;
            }
        });
        return returnStatus;
    }

    defaultSelectedStatus() {
        let getNextStatus = false;
        let defaultStatus;
        let defaultStatusFound = false;
        this.props.possibleStatus.map((singleStatus) => {
            // debugger;
            if (singleStatus.value == this.props.currentStatus) {
                getNextStatus = true;
            } else if (getNextStatus) {
                defaultStatus = singleStatus;
                getNextStatus = false;
                defaultStatusFound = true;
            } else if (!defaultStatusFound) {
                defaultStatus = singleStatus;
            }
        });
        return defaultStatus;
    }

    handlePopconfirmSaveBtn = (e) => {
        e.stopPropagation();
        if (this.formRef.current) {
            // Validate fields and then submit
            this.formRef.current
                .validateFields()
                .then((values) => {
                    this.submitForm(values);
                })
                .catch((error) => {
                    console.log(
                        'StatusChanger :: handlePopconfirmSaveBtn :: error ::',
                        error
                    );
                });
        }
    };

    render() {
        const { currentStatus, isFormSubmitting, error } = this.state;
        const {
            possibleStatus,
            tagClassName,
            srvc_id,
            is_restricted_view,
            TMS250414073641,
        } = this.props;
        let userRights = [];
        userRights = ConfigHelpers?.getUserRights(srvc_id);

        const saveButtonRights =
            userRights?.includes('CREATE') ||
            userRights?.includes('UPDATE') ||
            (ConfigHelpers.isServiceProvider() &&
                !ConfigHelpers.isUserOnfield());

        return (
            <>
                <div
                    className="wy-popconfirm-props"
                    onClick={(e) => {
                        e.stopPropagation();
                    }}
                >
                    {this.props.is_restricted_view &&
                    this.props.TMS250414073641 &&
                    ConfigHelpers.isServiceProvider() ? (
                        <Tag
                            className={`${tagClassName} ${this.props?.deleted ? 'gx-bg-grey gx-rounded-xs gx-p-1 gx-px-3 gx-text-white gx-ml-0 gx-pointer' : 'gx-rounded-xs gx-p-1 gx-px-3 gx-text-white gx-ml-0 gx-pointer'} `}
                            style={{
                                background: currentStatus.color,
                            }}
                        >
                            {currentStatus?.title || currentStatus?.label}
                        </Tag>
                    ) : (
                        <Popconfirm
                            showCancel={false}
                            placement="bottomLeft"
                            icon={null}
                            onConfirm={this.handlePopconfirmSaveBtn}
                            okText="Save"
                            okButtonProps={{
                                disabled: !saveButtonRights,
                                title:
                                    !saveButtonRights &&
                                    'Insufficient permissions',
                                size: 'middle',
                            }}
                            title={() => {
                                return (
                                    <Form
                                        ref={this.formRef}
                                        initialValues={{
                                            new_status:
                                                this.defaultSelectedStatus()
                                                    .value,
                                        }}
                                        layout="inline"
                                        size="medium"
                                    >
                                        <Form.Item
                                            label="New Status"
                                            name="new_status"
                                            className="gx-mb-2"
                                        >
                                            <Select
                                                style={{ width: 200 }}
                                                className="gx-ml-md-0 gx-ml-2"
                                            >
                                                {possibleStatus.map(
                                                    (singleStatus, index) =>
                                                        singleStatus.value !=
                                                            -1 &&
                                                        singleStatus.value !=
                                                            currentStatus.value && (
                                                            <Option
                                                                key={index}
                                                                value={
                                                                    singleStatus.value
                                                                }
                                                            >
                                                                <i
                                                                    className={`icon icon-circle gx-mr-2`}
                                                                    style={{
                                                                        color: singleStatus.color,
                                                                    }}
                                                                />
                                                                <Tooltip
                                                                    title={
                                                                        singleStatus.title
                                                                    }
                                                                >
                                                                    <span className="">
                                                                        {
                                                                            singleStatus.title
                                                                        }
                                                                    </span>
                                                                </Tooltip>
                                                            </Option>
                                                        )
                                                )}
                                            </Select>
                                        </Form.Item>
                                        {isFormSubmitting ? (
                                            <div className="gx-loader-view gx-loader-position">
                                                <CircularProgress />
                                            </div>
                                        ) : null}
                                        {error ? (
                                            <p className="gx-text-red">
                                                {error}
                                            </p>
                                        ) : null}
                                    </Form>
                                );
                            }}
                        >
                            <Tag
                                className={`${tagClassName} ${this.props?.deleted ? 'gx-bg-grey gx-rounded-xs gx-p-1 gx-px-3 gx-text-white gx-ml-0 gx-pointer' : 'gx-rounded-xs gx-p-1 gx-px-3 gx-text-white gx-ml-0 gx-pointer'} `}
                                style={{
                                    background: currentStatus.color,
                                }}
                            >
                                {currentStatus?.title || currentStatus?.label}
                            </Tag>
                        </Popconfirm>
                    )}
                </div>
            </>
        );
    }
}

export default StatusChanger;
