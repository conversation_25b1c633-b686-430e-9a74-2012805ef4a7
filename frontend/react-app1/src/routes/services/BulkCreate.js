import React, { Component } from 'react';
import { Form, Modal, Button, Spin } from 'antd';
import http_utils from '../../util/http_utils';
import BulkUploader from '../../components/wify-utils/BulkUploader';
import { getNewSubTaskFormMeta } from '../../components/WIFY/subtasks/helpers';
import ConfigHelpers from '../../util/ConfigHelpers';
import FormBuilder from 'antd-form-builder';
import CircularProgress from '../../components/CircularProgress';
import { getAddressFieldsMeta } from '../../util/CustomerHelpers';
import { priorities } from '../../util/helpers';
import RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';
import UserSelectorWidget from '../../components/wify-utils/UserSelectorWidget';
import moment from 'moment';
import { decodeFieldsMetaFrmJson } from '../../components/wify-utils/FieldCreator/helpers';
import { getSbtskTimeFieldsMetaFrReqCreation } from './helpers';

const protoUrl = '/services/bulk_creation/proto';
const overviewProto = '/services/bulk_creation/srvc_types';
const submitUrl = '/services/bulk_create_fr_vertical';

const tagsProto = {
    tags: [
        { value: 1, label: 'Apples' },
        { value: 2, label: 'Pears' },
    ],
    suggestions: [
        { value: 3, label: 'Auto built 1' },
        { value: 4, label: 'Auto built 2' },
        { value: 5, label: 'Auto built 3' },
        { value: 6, label: 'Auto built 4' },
    ],
};
class BulkCreate extends Component {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
        this.initApiUrls(this.props);
    }

    state = {
        render_helper: false,
        visible: false,
        isFormSubmitting: false,
        viewData: undefined,
        verticalData: undefined,
        isLoadingVerticalData: false,
        isLoadingViewData: false,
        editMode: this.props.editMode,
        error: '',
    };

    refresh() {
        this.setState({
            render_helper: !this.state.render_helper,
        });
    }

    initApiUrls(props) {
        this.submitUrl = submitUrl + '/' + (this.props.isCustReqs ? '1' : '0');
    }

    componentDidMount() {
        this.initViewData();
    }

    initViewData() {
        if (
            (this.state.editMode && this.state.visible) ||
            (!this.state.editMode &&
                this.state.viewData == undefined &&
                !this.state.isLoadingViewData)
        ) {
            this.setState({
                isLoadingViewData: true,
            });
            var params = {};
            const onComplete = (resp) => {
                this.setState({
                    isLoadingViewData: false,
                    viewData: resp.data,
                    error: '',
                });
            };
            const onError = (error) => {
                // console.log(error.response.status);
                this.setState({
                    isLoadingViewData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };
            http_utils.performGetCall(protoUrl, params, onComplete, onError);
        }
    }

    getDataFrVerticalSelected(value) {
        if (
            (this.state.editMode && this.state.visible) ||
            (!this.state.editMode && !this.state.isLoadingVerticalData)
        ) {
            this.setState({
                isLoadingVerticalData: true,
            });
            var params = {};
            const onComplete = (resp) => {
                this.setState({
                    isLoadingVerticalData: false,
                    verticalData: resp.data,
                    error: '',
                });
            };
            const onError = (error) => {
                // console.log(error.response.status);
                this.setState({
                    isLoadingVerticalData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };
            http_utils.performGetCall(
                overviewProto + '/' + value,
                params,
                onComplete,
                onError
            );
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevProps.editorItem != this.props.editorItem ||
            prevProps.showEditor != this.props.showEditor
        ) {
            this.setState(
                {
                    render_helper: !this.state.render_helper,
                    visible: this.props.showEditor,
                },
                function () {
                    if (this.props.showEditor && this.state.editMode) {
                        this.initViewData();
                    }
                }
            );
        }
    }

    handleOk = () => {
        this.setState({
            visible: false,
            isFormSubmitting: false,
        });
        this.updateClosureToParent();
    };

    updateClosureToParent() {
        if (this.props.onClose != undefined) {
            this.props.onClose();
        }
        this.setState({
            refreshOnUpdate: true,
            ...this.initState,
        });
    }

    tellParentToRefreshList(entry_id) {
        // console.log("Trying to to tell parent to refresh list");
        if (this.props.onDataModified != undefined) {
            this.props.onDataModified(entry_id);
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false,
            verticalData: undefined,
            verticalSelected: false,
        });
        this.updateClosureToParent();
    };

    getFieldsMetaFrBulkUpload() {
        let addressInfoMeta = this.getAddressInfoMeta().fields;
        const keysToExclude = ['address', 'location', 'clear_fields'];
        addressInfoMeta = addressInfoMeta.filter(
            (item) => !keysToExclude.includes(item.key)
        );

        return [
            ...this.getCustomerInfoMeta().fields,
            ...addressInfoMeta,
            ...this.getCustomFieldsFrmConfig(),
            ...this.getSPAuthoritySelectorMeta(),
            ...this.getRequestInfoMeta().fields,
            ...this.getSrvcTypeMeta().fields,
        ];
    }

    getCustomerInfoMeta() {
        const org_settings_config_data = this.props.orgSettingsData;
        const countryCode = org_settings_config_data?.country_code;
        const mobileDigit = org_settings_config_data?.mobile_digit;
        const meta = {
            formItemLayout: null,
            fields: [
                {
                    key: 'customer_info',
                    colSpan: 4,
                    render() {
                        return (
                            <fieldset>
                                <legend>
                                    <b>Customer information</b>
                                </legend>
                            </fieldset>
                        );
                    },
                },
                {
                    key: 'cust_mobile',
                    label: `Mobile(${countryCode})`,
                    required: true,
                    rules: [
                        {
                            pattern: new RegExp('^[0-9]*$'),
                            message: 'Incorrect number',
                        },
                        {
                            min: mobileDigit,
                            message: `Mobile (${countryCode}) must be ${mobileDigit} characters.`,
                        },
                        {
                            max: mobileDigit,
                            message: `Mobile (${countryCode}) must be ${mobileDigit} characters.`,
                        },
                    ],
                },
                {
                    key: 'cust_full_name',
                    label: 'Name',
                    required: true,
                    rules: [
                        {
                            max: 100,
                        },
                    ],
                },
                {
                    key: 'cust_email',
                    label: 'Email',
                    rules: [
                        {
                            type: 'email',
                            max: 100,
                        },
                    ],
                },
            ],
        };
        return meta;
    }

    getAddressInfoMeta() {
        const meta = {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'address',
                    colSpan: 4,
                    render() {
                        return (
                            <fieldset>
                                <legend>
                                    <b>Address</b>
                                </legend>
                            </fieldset>
                        );
                    },
                },
                ...getAddressFieldsMeta(
                    this.formRef,
                    () => {
                        this.forceUpdate();
                    },
                    undefined,
                    this.props.orgSettingsData
                ),
            ],
        };
        return meta;
    }

    getCustomFieldsFrmConfig() {
        const spcustomFields = [...(this.getSpCustomFieldsMeta() || [])];
        let meta = [...spcustomFields];
        return meta;
    }

    getSpCustomFieldsMeta() {
        let sp_cust_fields_json = [];
        let sp_config_data;
        if (ConfigHelpers.isServiceProvider()) {
            if (this.state.verticalData) {
                sp_config_data =
                    this.state?.verticalData?.verticals_data[0]
                        ?.sp_custom_fields?.sp_cust_fields_json;
            }
            if (sp_config_data) {
                sp_cust_fields_json = decodeFieldsMetaFrmJson(sp_config_data);
            }
        }
        return sp_cust_fields_json;
    }

    getRequestInfoMeta() {
        let verticalNature =
            this.state.verticalData?.verticals_data?.[0]?.sp_custom_fields
                ?.vertical_nature;
        const startOfDay = '5:00AM';
        const endOfDay = '11:45PM';

        const meta = {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'request_description',
                    colSpan: 4,
                    label: 'Description',
                    widget: 'textarea',
                    required: true,
                },
                {
                    key: 'request_req_date',
                    label: 'Req. Service Date',
                    colSpan: 2,
                    widgetProps: {
                        disabledDate: (current) =>
                            current && current > moment().add(2, 'month'),
                        style: {
                            width: '100%',
                        },
                        onChange: (value, dateString) => {
                            this.formRef.current.setFieldsValue({
                                request_req_date: moment.utc(dateString),
                            });
                        },
                    },
                    widget: 'date-picker',
                },
                ...getSbtskTimeFieldsMetaFrReqCreation(
                    startOfDay,
                    endOfDay,
                    verticalNature,
                    this.formRef,
                    this.state.viewData,
                    () => {
                        this.refresh();
                    }
                ),
                {
                    key: 'request_priority',
                    label: 'Priority',
                    colSpan: 2,
                    widget: 'select',
                    options: priorities, // to be loaded from API
                    required: true,
                },
                {
                    key: 'request_labels',
                    label: 'Labels',
                    colSpan: 2,
                    widget: RemoteSourceSelect,
                    options: tagsProto.suggestions,
                    renderView: (values) => (
                        <div>
                            {typeof values.map == 'function' &&
                                values.map((value) => value.label + ',')}
                        </div>
                    ),
                    widgetProps: {
                        mode: 'tags',
                        url: '/searcher',
                        placeholder: 'Start typing..',
                        params: {
                            srvc_type_id: this.props.srvcDetails?.srvc_id,
                            fn: 'getServiceRequestLabels',
                        },
                        widgetProps: {
                            mode: 'tags',
                            style: {
                                width: '100%',
                            },
                        },
                    },
                },
                {
                    key: 'request_cc_users',
                    label: 'CC users',
                    widget: UserSelectorWidget,
                    widgetProps: {
                        mode: 'multiple',
                    },
                    renderView: (values) => (
                        <div>
                            {typeof values.map == 'function' &&
                                values.map((value) => value.label + ',')}
                        </div>
                    ),
                    colSpan: 2,
                },
                {
                    key: 'creation_date',
                    label: 'Creation Date',
                    colSpan: 2,
                    tooltip:
                        'You can specify a past creation date, incase you are creating entry on a different day. You can ignore this if you are creating the request on same day',
                    widgetProps: {
                        disabledDate: (current) =>
                            current && current > moment().endOf('day'),
                        style: {
                            width: '100%',
                        },
                        onChange: (value, dateString) => {
                            this.formRef.current.setFieldsValue({
                                creation_date: moment.utc(dateString),
                            });
                        },
                    },
                    widget: 'date-picker',
                },
                {
                    key: 'initial_status',
                    label: 'Initial Status (Optional)',
                    colSpan: 2,
                    widget: 'select',
                    tooltip:
                        'Specify starting status so that the request will be auto moved from open status to the specified status. Please keep in mind that notifications to customer will be triggered(if any) as per configuration for the specified status',
                    options: this.state.viewData?.statuses,
                },
            ],
        };
        return meta;
    }

    getSrvcTypeMeta() {
        const meta = {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'srvc_type_id',
                    label: 'Service Type',
                    colSpan: 2,
                    widget: 'select',
                    options:
                        this.state.verticalData?.verticals_data[0]?.srvc_types, // to be loaded from API
                    required: true,
                },
            ],
        };
        return meta;
    }

    getSPAuthoritySelectorMeta() {
        let sp_authorities_owners_list =
            this.state.verticalData
                ?.srvc_prvdr_role_wise_authorities_users_list || [];
        return sp_authorities_owners_list;
    }

    getMeta() {
        const meta = {
            columns: 2,
            formItemLayout: null,
            fields: [
                {
                    key: 'select_vertical',
                    label: 'Select Vertical',
                    widget: 'select',
                    required: true,
                    placeholder: 'Select vertical',
                    options: this.state.viewData,
                    widgetProps: {
                        onChange: (value) => {
                            //do an api call so we can get data for meta
                            this.getDataFrVerticalSelected(value);
                        },
                    },
                },
            ],
        };
        return meta;
    }

    render() {
        const {
            isFormSubmitting,
            visible,
            error,
            verticalSelected,
            isLoadingVerticalData,
            verticalData,
        } = this.state;
        var editorTitle = 'Bulk Create Site For Vertical';
        return visible ? (
            <Modal
                title={
                    <span>
                        <i className={` gx-mr-2 h1`}></i> {editorTitle}
                    </span>
                }
                visible={visible}
                onOk={this.handleOk}
                confirmLoading={isFormSubmitting}
                width={900}
                style={{
                    marginTop: '-70px',
                }}
                bodyStyle={{
                    padding: '18px',
                    paddingTop: '0px',
                }}
                footer={null}
                onCancel={this.handleCancel}
            >
                <Form
                    className="gx-mt-3"
                    layout="vertical"
                    ref={this.formRef}
                    onFinish={(data) => {
                        this.submitForm(data);
                        this.setState({ isEdited: false });
                    }}
                >
                    <FormBuilder meta={this.getMeta()} form={this.formRef} />
                </Form>
                {isLoadingVerticalData ? (
                    <div className="gx-loader-view gx-loader-position">
                        <CircularProgress />
                    </div>
                ) : verticalData == undefined ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <BulkUploader
                        // demoMode
                        // renderFormsForRows
                        // debugMode
                        onDataModified={(entry_ids) =>
                            this.tellParentToRefreshList(0)
                        }
                        submitUrl={this.submitUrl}
                        dataProto={this.getFieldsMetaFrBulkUpload()}
                        timeFormatMsg
                        orgSettingsData={this.props.orgSettingsData}
                    />
                )}
            </Modal>
        ) : (
            <></>
        );
    }
}

export default BulkCreate;
