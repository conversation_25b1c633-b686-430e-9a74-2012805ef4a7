import { CheckOutlined, SyncOutlined } from '@ant-design/icons';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    Col,
    Row,
    Skeleton,
    Table,
    Tag,
    Tooltip,
} from 'antd';
import React, { useEffect, useState } from 'react';
import SkeletonLoader from '../../components/WIFY/WifyComponents/SkeletonLoader';
import {
    convertUTCToDisplayTime,
    getRandomTagBgColor,
    isMobileView,
} from '../../util/helpers';
import { decodeFieldsMetaFrmJson } from '../../components/wify-utils/FieldCreator/helpers';
import { getSingleSelectDropdowns } from '../setup/automation-deployment/job-broadcast/utils';
import UserNameById from '../../components/wify-utils/UserName';

import http_utils from '../../util/http_utils';
import ConfigHelpers from '../../util/ConfigHelpers';
import { getIsSrvcReqLock } from './project/billing/helpers';
const protoUrl = '/services/profit_and_loss';
const submitUrl = '/services/profit_and_loss/update';

const getRevenueBreakdownColMeta = () => {
    return [
        {
            title: 'ITEM NAME',
            dataIndex: 'item_name',
            key: 'item_name',
        },
        {
            title: 'QUANTITY',
            dataIndex: 'qty',
            key: 'qty',
        },
        {
            title: 'EXPECTED REVENUE',
            dataIndex: 'expectedRevenue',
            key: 'expectedRevenue',
        },
        {
            title: 'ACTUAL REVENUE',
            dataIndex: 'actualRevenue',
            key: 'actualRevenue',
        },
    ];
};

const costbreakdownCols = [
    {
        title: 'NAME',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: 'RESOURCE TYPE',
        dataIndex: 'resource_type',
        key: 'resource_type',
    },
    {
        title: 'TECHNICIAN COST',
        dataIndex: 'technicianCost',
        key: 'technicianCost',
    },
    {
        title: 'MANDAYS',
        dataIndex: 'mandays',
        key: 'mandays',
    },
    {
        title: 'EXPECTED COST',
        dataIndex: 'expectedCost',
        key: 'expectedCost',
    },
    {
        title: 'ACTUAL COST',
        dataIndex: 'actualCost',
        key: 'actualCost',
    },
];

const costbreakdownData = [
    {
        key: '1',
        name: 'John Smith',
        resourceType: 'Inhouse',
        technicianCost: '₹400.00',
        mandays: 2,
        expectedCost: '₹1,000.00',
        actualCost: '₹800.00',
    },
    {
        key: '2',
        expectedCost: 'Additional Cost',
        actualCost: '₹200.00',
    },
    {
        key: '3',
        expectedCost: 'Deductions',
        actualCost: <span className="gx-text-danger">-₹100.00</span>,
    },
    {
        key: '4',
        expectedCost: 'Total Cost',
        actualCost: '₹900.00',
    },
];

const profitAndLossData = [
    {
        key: 'gm_per',
        label: 'GM%',
        value: '',
        className: 'gx-text-green',
    },
    {
        key: 'net_gm',
        label: 'Net GM',
        value: '',
        className: 'gx-text-green',
    },
    {
        key: 'total_revenue',
        label: 'Total Revenue',
        value: '',
        className: 'gx-fontweight-bold gx-text-black',
    },
    {
        key: 'total_cost',
        label: 'Total Cost',
        value: '',
        className: 'gx-fontweight-bold gx-text-black',
    },
];

const ProfitAndLoss = ({
    spConfigData,
    srvcReqData,
    orgNickName,
    srvcTypeId,
    srvcReqId,
    roleList,
    locGrpsName,
    allAuthoritiesOfVertical,
    srvcConfigData,
}) => {
    const [loading, setLoading] = useState(false);
    const [initialLoading, setInitialLoading] = useState(false);
    const [syncLoading, setSyncLoading] = useState(false);
    const [notifications, setNotifications] = useState({
        synced: false,
        valuesUpToDate: false,
        isSyncing: false,
        valueNeedToChange: false,
        syncingInProgress: false,
    });

    const [viewData, setViewData] = useState(undefined);
    const [prevViewData, setPrevViewData] = useState(undefined);
    const [error, setError] = useState(undefined);
    const haveAccessToSync = ConfigHelpers.doesUserHaveOneOfTheRole(
        spConfigData?.roles_who_can_sync_pl_values
    );

    useEffect(() => {
        initViewData();
    }, []);

    useEffect(() => {
        if (!viewData) {
            return;
        }
        if (viewData?.show_sync_option) {
            setNotifications((prev) => {
                return { ...prev, valueNeedToChange: true };
            });
        } else {
            setNotifications((prev) => {
                return { ...prev, valuesUpToDate: true };
            });
        }
    }, [viewData?.show_sync_option]);

    const initViewData = (frSync) => {
        if ((viewData == undefined && !loading) || frSync) {
            setInitialLoading(true);
            setLoading(true);
            if (frSync) {
                setSyncLoading(true);
            }
            let params = {};
            params['vertical_id'] = spConfigData?.entry_id;
            params['org_nick_name'] = orgNickName;
            params['sync_data'] = frSync;

            const onComplete = (resp) => {
                setViewData(resp.data);
                setLoading(false);
                if (frSync) {
                    setSyncLoading(false);
                    setNotifications((prev) => {
                        return {
                            ...prev,
                            isSyncing: true,
                            valueNeedToChange: false,
                        };
                    });
                }
            };

            const onError = (error) => {
                setInitialLoading(false);
                setLoading(false);
                if (frSync) {
                    setSyncLoading(false);
                }
                setError(http_utils.decodeErrorToMessage(error));
            };
            const url = protoUrl + '/' + srvcTypeId + '/' + srvcReqId;

            http_utils.performGetCall(url, params, onComplete, onError);
        }
    };

    const getProfitAndLossWidgetTopData = () => {
        return profitAndLossData.map((singleObj) => {
            let updatedValue = viewData?.[singleObj.key];
            let updatedClassName = singleObj.className;

            // Check for null or empty values and set to '-'
            if (updatedValue === null || updatedValue === '') {
                updatedValue = '-';
            }

            if (
                (singleObj.key === 'gm_per' || singleObj.key === 'net_gm') &&
                parseFloat(updatedValue) < 0 &&
                updatedValue != '-Infinity'
            ) {
                updatedClassName = 'gx-text-red';
            }

            if (updatedValue == '-Infinity' && singleObj.key === 'gm_per') {
                updatedClassName = 'gx-text-black';
            }

            if (updatedValue !== '-' && (updatedValue || updatedValue == 0)) {
                if (singleObj.key === 'gm_per') {
                    if (
                        updatedValue == '-Infinity' ||
                        updatedValue == 'Infinity'
                    ) {
                        updatedValue = '0%';
                    } else {
                        updatedValue = isNaN(updatedValue)
                            ? '0%'
                            : `${updatedValue}%`;
                    }
                } else {
                    updatedValue =
                        parseFloat(updatedValue) < 0
                            ? `-₹${Math.abs(updatedValue)}`
                            : `₹${updatedValue}`;
                }
            }
            return {
                ...singleObj,
                value: updatedValue,
                className: updatedClassName,
            };
        });
    };

    const handleNotificationSync = () => {
        setPrevViewData(viewData);
        initViewData(true);
    };
    /**
     * Retrieves a filtered list of authority objects.
     *
     * This function filters the global array `allAuthoritiesOfVertical` by checking if a corresponding value exists
     * in the `srvcReqData` object for each authority's key.
     *
     * @returns {Array|undefined} An array of authority objects if any are found; otherwise, undefined.
     */
    const getAuthorityWithRoles = () => {
        return allAuthoritiesOfVertical?.filter(
            (singleAuthorityMeta) => srvcReqData[singleAuthorityMeta.key]
        );
    };

    const handleSyncCancel = () => {
        setViewData(prevViewData);
        setPrevViewData(undefined);
        setNotifications((prev) => {
            return { ...prev, isSyncing: false, valueNeedToChange: true };
        });
    };

    const handleSyncUpdate = () => {
        let params = {};
        params['vertical_id'] = spConfigData?.entry_id;
        params['org_nick_name'] = orgNickName;

        setNotifications((prev) => {
            return { ...prev, syncingInProgress: true, isSyncing: false };
        });
        const onComplete = (resp) => {
            setNotifications((prev) => {
                return { ...prev, synced: true, syncingInProgress: false };
            });
        };
        const onError = (error) => {
            setNotifications((prev) => {
                return { ...prev, synced: true, syncingInProgress: false };
            });
        };

        const url = submitUrl + '/' + srvcTypeId + '/' + srvcReqId;
        http_utils.performPutCall(url, params, onComplete, onError);
    };

    /**
     * Renders the UI for displaying all authorities.
     *
     * This component returns a scrolling wrapper containing a list of authority elements.
     * Each authority is displayed within a Tooltip and a Tag, showing the authority's associated UserNameById and label.
     *
     * @returns {JSX.Element} A React element that renders the list of authorities.
     */
    const showAllAuthoritiesUI = () => {
        return (
            <div className="scrolling_wrapper gx-mr-3">
                {getAuthorityWithRoles()?.length > 0 &&
                    getAuthorityWithRoles()?.map((authority) => (
                        <Tooltip
                            title={() => {
                                return (
                                    <UserNameById
                                        id={srvcReqData[authority.key]}
                                        customError={'Missing'}
                                    />
                                );
                            }}
                            placement="top"
                        >
                            <div className="gx-text-center gx-d-inline-block">
                                <Tag
                                    color={`${getRandomTagBgColor()}`}
                                    className="wy-tag-line-height-adjust gx-mb-0"
                                >
                                    <UserNameById
                                        id={srvcReqData[authority.key]}
                                        customError={'-'}
                                    />
                                    <br />
                                    <small className="gx-mb-0 gx-text-center">
                                        {authority.label}
                                    </small>
                                </Tag>
                            </div>
                        </Tooltip>
                    ))}
            </div>
        );
    };

    const showAllNotificationsUI = () => {
        return (
            <>
                {notifications?.synced && (
                    <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-sync-popup wy-bg-pop-success">
                        <div className="gx-d-flex gx-align-items-center">
                            <CheckOutlined className="gx-text-success gx-mr-2 gx-font-weight-bold" />
                            <p className="gx-mb-0">Synced successfully</p>
                        </div>
                        <div>
                            <Button
                                success
                                type="primary"
                                className="gx-mb-0 gx-mr-2 wy-sync-pop-btn-success"
                                size={isMobileView() && 'small'}
                                onClick={() => {
                                    setNotifications((prev) => {
                                        return { ...prev, synced: false };
                                    });
                                }}
                            >
                                Ok
                            </Button>
                        </div>
                    </div>
                )}
                {notifications?.syncingInProgress && (
                    <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-sync-popup wy-bg-pop-success">
                        <div className="gx-d-flex gx-align-items-center">
                            <CheckOutlined className="gx-text-success gx-mr-2 gx-font-weight-bold" />
                            <p className="gx-mb-0">Syncing in progress...</p>
                        </div>
                    </div>
                )}
                {notifications?.valuesUpToDate && (
                    <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-sync-popup wy-bg-pop-success">
                        <div className="gx-d-flex gx-align-items-center">
                            <CheckOutlined className="gx-text-success gx-mr-2 gx-font-weight-bold" />
                            <p className="gx-mb-0">Values are up to date.</p>
                        </div>
                        <div>
                            <Button
                                success
                                type="primary"
                                className="gx-mb-0 gx-mr-2 wy-sync-pop-btn-success"
                                size={isMobileView() && 'small'}
                                onClick={() => {
                                    setNotifications((prev) => {
                                        return {
                                            ...prev,
                                            valuesUpToDate: false,
                                        };
                                    });
                                }}
                            >
                                Ok
                            </Button>
                        </div>
                    </div>
                )}
                {notifications?.isSyncing && (
                    <div className="gx-d-flex gx-align-items-center gx-justify-content-between wy-sync-popup">
                        <div className="gx-d-flex gx-align-items-center">
                            <SyncOutlined className="gx-text-primary gx-mr-2" />
                            <p className="gx-mb-0">Confirm to update values</p>
                        </div>
                        <div className="gx-d-flex gx-align-items-center">
                            <Button
                                type="link"
                                className="gx-m-0 gx-mr-2 gx-text-danger"
                                size={isMobileView() && 'small'}
                                onClick={handleSyncCancel}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="primary"
                                className="gx-m-0"
                                size={isMobileView() && 'small'}
                                onClick={handleSyncUpdate}
                            >
                                Update
                            </Button>
                        </div>
                    </div>
                )}
                {notifications?.valueNeedToChange && (
                    <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-sync-popup">
                        <div className="gx-d-flex gx-align-items-center">
                            <SyncOutlined className="gx-text-primary gx-mr-2" />
                            <p className="gx-mb-0">
                                Values are changed in master,{' '}
                                {haveAccessToSync
                                    ? 'click to change to new values'
                                    : 'Please contact admin'}
                            </p>
                        </div>
                        <Button
                            type="primary"
                            icon={<SyncOutlined spin={syncLoading} />}
                            onClick={handleNotificationSync}
                            className="gx-mb-0"
                            size={isMobileView() && 'small'}
                            disabled={
                                !haveAccessToSync ||
                                getIsSrvcReqLock(srvcReqData, true)
                            }
                        >
                            Sync
                        </Button>
                    </div>
                )}
            </>
        );
    };

    const requestDetailsUI = ({ label, value, loading }) => (
        <div className="wy-request-details-item">
            <div className="wy-request-details-label gx-text-gray gx-fs-sm">
                {loading ? (
                    <div className="wy-pl-animate-pulse wy-loading-description-label-120"></div>
                ) : (
                    label
                )}
            </div>
            <div className="description-value gx-fs-lg gx-text-black">
                {loading ? (
                    <div className="wy-pl-animate-pulse wy-loading-description-label-80"></div>
                ) : (
                    value
                )}
            </div>
        </div>
    );

    /**
     * Retrieves revenue column configuration based on service type ID.
     *
     * @param {number|string} srvc_type_id - The service type ID to filter configurations.
     * @param {Object} configData - The configuration data containing revenue column metadata.
     * @param {boolean} [isSrvcTypeConfigured=false] - Flag to check if service type is configured.
     * @returns {boolean|Array<Object>} - Returns `true` if service type is configured and has valid config,
     *                                    `false` if not configured,
     *                                    or an array of relevant configuration objects.
     */
    const getRevenueColumnConfig = ({
        srvc_type_id,
        configData,
        isSrvcTypeConfigured = false,
    }) => {
        const srvcTypeRevenueConfig = JSON.parse(
            configData?.srvc_type_revenue_column_meta || '[]'
        );

        const verticalRevenueConfig = JSON.parse(
            configData?.revenue_column_meta || '[]'
        );

        // Check if srvcTypeRevenueConfig has relevant keys with values and matches srvc_type_id
        const validConfig = srvcTypeRevenueConfig.filter(
            (config) =>
                config.srvc_type == srvc_type_id &&
                (config.item_name || config.qty || config.sku)
        );
        if (isSrvcTypeConfigured) {
            return validConfig.length > 0 ? true : false;
        } else {
            return validConfig.length > 0 ? validConfig : verticalRevenueConfig;
        }
    };

    const getRevenueBreakdownRowData = () => {
        let customFields;
        const srvcTypeRevenueConfig = JSON.parse(
            spConfigData?.srvc_type_revenue_column_meta || '[]'
        );
        const revenueConfig = JSON.parse(
            spConfigData?.revenue_column_meta || '[]'
        );
        const mappingConfig = getRevenueColumnConfig({
            srvc_type_id: srvcTypeId,
            configData: spConfigData,
        });

        // Decode custom fields based on the selected configuration
        const isSrvcTypeConfig = getRevenueColumnConfig({
            srvc_type_id: srvcTypeId,
            configData: spConfigData,
            isSrvcTypeConfigured: true,
        });

        if (isSrvcTypeConfig) {
            customFields = decodeFieldsMetaFrmJson(
                srvcConfigData?.srvc_cust_fields_json
            );
        } else {
            customFields = decodeFieldsMetaFrmJson(
                spConfigData?.sp_cust_fields_json
            );
        }
        // console.log('srvcTypeRevenueConfig', customFields);
        const singleSelectFields = getSingleSelectDropdowns(customFields);
        let skuKey;
        let skuValue;
        let isSkuKeySelectField;
        if (!mappingConfig.length) return [];

        const revenueDataRows = mappingConfig.map((configField) => {
            const rowDataObj = {};

            Object.entries(configField).forEach(([key, value]) => {
                let fieldValue = viewData?.revenue_master_data[value];

                const singleSelectFound = singleSelectFields.find(
                    (field) =>
                        field.key === value &&
                        viewData?.revenue_master_data[field.key]
                );

                if (singleSelectFound?.options?.length) {
                    const matchedOption = singleSelectFound.options.find(
                        (option) =>
                            option.value ===
                            viewData?.revenue_master_data[singleSelectFound.key]
                    );
                    if (matchedOption) fieldValue = matchedOption.label;
                }

                rowDataObj[key] = fieldValue;
                if (key == 'sku') {
                    const singleSelectField = singleSelectFields.find(
                        (field) => field.key === value
                    );
                    if (singleSelectField?.options?.length) {
                        isSkuKeySelectField = true;
                        const matchedOption = singleSelectField.options.find(
                            (option) => option.value === srvcReqData[value]
                        );
                        if (matchedOption) skuKey = matchedOption.label;
                    }
                }
            });

            if (!skuKey) skuKey = configField?.sku;

            if (isSkuKeySelectField) {
                skuValue = skuKey;
            } else {
                skuValue = srvcReqData[configField?.sku];
            }

            if (skuValue && skuValue) {
                rowDataObj['expectedRevenue'] = formatCurrency(
                    viewData?.revenue_master_data[`${skuValue}_expectedRevenue`]
                );
                rowDataObj['actualRevenue'] = formatCurrency(
                    viewData?.revenue_master_data[`${skuValue}_actualRevenue`]
                );
            }
            return rowDataObj;
        });

        // show additional revenue and total revenue if configured
        if (spConfigData?.sp_additional_revenue_field) {
            const addtionalRevenueRows = [
                {
                    expectedRevenue: 'Additional Revenue',
                    actualRevenue: formatCurrency(viewData?.additional_revenue),
                },
                {
                    expectedRevenue: 'Total Revenue',
                    actualRevenue: formatCurrency(viewData?.total_revenue),
                },
            ];

            return [...revenueDataRows, ...addtionalRevenueRows];
        }

        return [...revenueDataRows];
    };

    const getSelectDropdowns = (all_user_fields = []) => {
        return all_user_fields.filter(
            (item) =>
                item.widget === 'select' ||
                item.widget === 'radio-group' ||
                item.widget === 'checkbox-group'
        );
    };

    const formatCurrency = (value) => {
        if (value === undefined || value === null) return '';
        // append negative sign if value is negative
        const absValue = Math.abs(value);
        const sign = value < 0 ? '-' : '';
        return `${sign}₹${absValue}`;
    };

    const getCostBreakdownRowData = () => {
        const selectDropDown = getSelectDropdowns(
            viewData?.user_custom_fields?.translatedFields
        );
        const userResourceTypeKey = spConfigData.userFieldFrResourceType; // Get the key from configdata
        const technician_data_fr_cost_breakdown =
            viewData?.technician_data_fr_cost_breakdown;
        let costBreakdownData = [];
        if (technician_data_fr_cost_breakdown) {
            technician_data_fr_cost_breakdown.map((item, index) => {
                let fieldValue = item[userResourceTypeKey];

                if (userResourceTypeKey === 'user_role') {
                    try {
                        let parsedValue = JSON.parse(fieldValue);
                        if (Array.isArray(parsedValue)) {
                            fieldValue = parsedValue
                                .map((value) => {
                                    const matchedRole = roleList?.find(
                                        (role) => role.value === value
                                    );
                                    return matchedRole
                                        ? matchedRole.label
                                        : value;
                                })
                                .join(', ');
                        }
                    } catch (error) {
                        console.error('Error parsing user_role field:', error);
                        fieldValue = fieldValue;
                    }
                } else if (userResourceTypeKey === 'user_reporting_to') {
                    fieldValue = fieldValue ? (
                        <UserNameById id={fieldValue} />
                    ) : (
                        ''
                    );
                } else {
                    let selectFieldFound = selectDropDown.find(
                        (field) => field.key === userResourceTypeKey
                    );
                    if (selectFieldFound?.options?.length) {
                        if (Array.isArray(fieldValue)) {
                            fieldValue = fieldValue
                                .map((value) => {
                                    const matchedOption =
                                        selectFieldFound.options.find(
                                            (option) => option.value === value
                                        );
                                    return matchedOption
                                        ? matchedOption.label
                                        : value;
                                })
                                .join(', ');
                        } else {
                            const matchedOption = selectFieldFound.options.find(
                                (option) => option.value === fieldValue
                            );
                            if (matchedOption) fieldValue = matchedOption.label;
                        }
                    }
                }

                costBreakdownData.push({
                    key: (index + 1).toString(),
                    name: <UserNameById id={item.user_id} />,
                    resource_type: fieldValue,
                    technicianCost: formatCurrency(
                        viewData?.technician_master_data[
                            `${viewData.sku}_technicianCost`
                        ]
                    ),
                    mandays: item.mandays,
                    expectedCost: formatCurrency(
                        viewData?.technician_master_data[
                            `${item.user_id}_expectedCost`
                        ]
                    ),
                    actualCost: formatCurrency(
                        viewData?.technician_master_data[
                            `${item.user_id}_actualCost`
                        ]
                    ),
                });
            });
        }

        // Add additional rows at the end
        costBreakdownData.push(
            {
                expectedCost: 'Additional Cost',
                actualCost: formatCurrency(viewData?.additional_cost),
            },
            {
                expectedCost: 'Discount',
                actualCost: (
                    <span className="gx-text-danger">
                        {formatCurrency(viewData?.discount)}
                    </span>
                ),
            },
            {
                expectedCost: 'Deductions',
                actualCost: (
                    <span className="gx-text-danger">
                        {formatCurrency(viewData?.deduction)}
                    </span>
                ),
            },
            {
                expectedCost: 'Total Cost',
                actualCost: formatCurrency(viewData?.total_cost),
            }
        );
        return costBreakdownData;
    };

    const isLambdaConfigured = spConfigData?.lambda_arn_profit_loss;
    if (!isLambdaConfigured) {
        return (
            <div>
                <Alert
                    message="Configure lambda for missing values"
                    type="warning"
                />
            </div>
        );
    }

    return (
        <div className="wy-pnl-wrapper">
            {!loading && showAllNotificationsUI()}

            {loading || syncLoading ? (
                <div className="gx-mb-4">
                    <SkeletonLoader box gridBox={isMobileView() ? 2 : 4} />
                    {isMobileView() && <SkeletonLoader box gridBox={2} />}
                </div>
            ) : (
                <>
                    <Row gutter={[10, 10]}>
                        {getProfitAndLossWidgetTopData().map((item, index) => (
                            <Col md={6} sm={12} xs={12} key={index}>
                                <div className="gx-w-100 wy-pl-wrapper-card wy-pl-height-adjust gx-mb-0">
                                    <div className="wy-pL-card-border"></div>
                                    <div className="gx-text-gray gx-fs-sm gx-mb-2">
                                        {item.label}
                                    </div>
                                    <div
                                        className={`${item.className} gx-fs-xl`}
                                    >
                                        {item.value}
                                    </div>
                                </div>
                            </Col>
                        ))}
                    </Row>
                </>
            )}
            <div className="gx-mt-4">
                <div className="wy-pl-wrapper-card gx-mt-4">
                    <div className="wy-request-details-wrapper">
                        <p className="gx-fs-18 gx-text-black">
                            Request Details
                        </p>
                        <div className="wy-request-details-grid">
                            {requestDetailsUI({
                                label: 'Vertical',
                                value: spConfigData?.vertical_title,
                            })}
                            {requestDetailsUI({
                                label: 'Location Group',
                                value: locGrpsName || '-',
                            })}
                            {requestDetailsUI({
                                label: 'Order Date',
                                value:
                                    convertUTCToDisplayTime(
                                        srvcReqData?.latestOpenStatus?.time
                                    ) || '-',
                            })}
                            {requestDetailsUI({
                                label: 'Completion Date',
                                value:
                                    convertUTCToDisplayTime(
                                        srvcReqData?.latestClosedStatus?.time
                                    ) || '-',
                            })}
                        </div>
                    </div>
                </div>
                <div className="wy-pl-wrapper-card">
                    {getAuthorityWithRoles()?.length === 0 ? (
                        <div>
                            <Alert
                                message="No Authorities selected for this request"
                                type="warning"
                                showIcon
                                className="gx-mb-0"
                            />
                        </div>
                    ) : (
                        <div>
                            <p className="gx-fs-18 gx-text-black">
                                Authorities
                            </p>
                            {loading ? (
                                <div className="gx-mb-2">
                                    <SkeletonLoader box />
                                </div>
                            ) : (
                                showAllAuthoritiesUI()
                            )}
                        </div>
                    )}
                </div>
            </div>
            <div className="gx-mt-3 gx-mb-2 wy-pl-wrapper-card">
                <p className="gx-fs-18 gx-mb-1">Revenue Breakdown</p>
                {loading ? (
                    <div className="gx-mb-4">
                        <Skeleton.Input
                            className="wy_skeleton_loading_box"
                            active
                        />
                    </div>
                ) : (
                    <div className="table-responsive-wy wy-pl-table-style  wy-pl-table-style-border2">
                        <Table
                            columns={getRevenueBreakdownColMeta()}
                            dataSource={getRevenueBreakdownRowData()}
                            pagination={false}
                            className="gx-border"
                        />
                    </div>
                )}
            </div>
            <div className="gx-mt-3 gx-mb-2 wy-pl-wrapper-card">
                <p className="gx-fs-18 gx-mb-1">Cost Breakdown</p>
                {loading ? (
                    <div className="gx-mb-4">
                        <Skeleton.Input
                            className="wy_skeleton_loading_box"
                            active
                        />
                    </div>
                ) : (
                    <div
                        className={`${getCostBreakdownRowData()?.length > 0 && 'wy-cost-breakdown-table'} table-responsive-wy wy-table-footer-wrapper wy-pl-table-style-border wy-pl-table-style`}
                    >
                        <Table
                            columns={costbreakdownCols}
                            dataSource={getCostBreakdownRowData()}
                            pagination={false}
                            className="gx-border"
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default ProfitAndLoss;
