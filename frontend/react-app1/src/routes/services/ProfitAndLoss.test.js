import React from 'react';
import { render, screen } from '@testing-library/react';
import <PERSON>itAndLoss from './ProfitAndLoss';
import http_utils from '../../util/http_utils';

beforeEach(() => {
    console.info('Console logs disabled!');
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'group').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
});

jest.mock('../../util/helpers', () => ({
    convertUTCToDisplayTime: (time) => time,
    getRandomTagBgColor: () => 'blue',
    isMobileView: () => false,
}));

jest.mock('../../util/ConfigHelpers', () => ({
    doesUserHaveOneOfTheRole: () => true,
}));

const mockApiResp1 = {
    revenue_master_data: {
        item1: 'Test Item',
        quantity: 10,
        expected: 500,
        actual: 450,
    },
    technician_master_data: {},
    user_custom_fields: {
        translatedFields: [],
    },
    show_sync_option: false,
    sku: 'Rate',
    total_cost: 0,
    total_revenue: null,
    net_gm: null,
    gm_per: 'NaN',
    additional_cost: 0,
    deduction: 0,
    discount: 0,
    skuChanged: false,
    show_sync_option: false,
};

const mockApiResp2 = {
    revenue_master_data: {
        item1: 'Different Item',
        quantity: 20,
        expected: 1000,
        actual: 950,
    },
    technician_master_data: {},
    user_custom_fields: {
        translatedFields: [],
    },
    show_sync_option: true,
    sku: 'Test Sku',
    total_cost: 200,
    total_revenue: 1000,
    net_gm: 800,
    gm_per: '80%',
    additional_cost: 10,
    deduction: 5,
    discount: 0,
    skuChanged: true,
    show_sync_option: true,
};

jest.mock('../../util/http_utils', () => ({
    performGetCall: (url, params, onComplete) => {
        onComplete({
            data: mockApiResp1,
        });
    },
    decodeErrorToMessage: (error) => error,
}));

const mockUserCustFields = {
    translatedFields: [
        {
            key: 'user_role',
            widget: 'select',
            options: [
                { value: 'admin', label: 'Admin' },
                { value: 'technician', label: 'Technician' },
            ],
        },
    ],
};

const mockAssigneeDetails = [
    {
        usr_id: 1,
        form_data: { user_role: ['admin'] },
        mandays: 5,
    },
    {
        usr_id: 2,
        form_data: { user_role: ['technician'] },
        mandays: 3,
    },
];

const mockRoleList = [
    { value: 'admin', label: 'Admin' },
    { value: 'technician', label: 'Technician' },
];

const mockSrvcConfigData = {
    sp_cust_fields_json: '{}',
    userFieldFrResourceType: 'user_role',
    revenue_column_meta:
        '[{"item_name":"item1","qty":"quantity","expectedRevenue":"expected","actualRevenue":"actual"}]',
    lambda_arn_profit_loss: 'arn:aws:lambda:region:account:function:name',
    vertical_title: 'Test Vertical',
};

const mockSrvcReqData = {
    quantity: 10,
    expected: '$500',
    actual: '$450',
    latestOpenStatus: { key: 'open', time: '2025-03-10T12:00:00Z' },
    latestClosedStatus: { key: 'closed', time: '2025-03-11T15:00:00Z' },
};

describe('ProfitAndLoss Component', () => {
    it('renders without crashing', () => {
        render(
            <ProfitAndLoss
                spConfigData={mockSrvcConfigData}
                userCustFields={mockUserCustFields}
                assigneeDetails={mockAssigneeDetails}
                roleList={mockRoleList}
                srvcReqData={mockSrvcReqData}
                orgNickName="test-org"
                srvcTypeId="test-type"
                srvcReqId="test-req"
            />
        );
    });

    it('renders revenue breakdown table', () => {
        render(
            <ProfitAndLoss
                spConfigData={mockSrvcConfigData}
                userCustFields={mockUserCustFields}
                assigneeDetails={mockAssigneeDetails}
                roleList={mockRoleList}
                srvcReqData={mockSrvcReqData}
                orgNickName="test-org"
                srvcTypeId="test-type"
                srvcReqId="test-req"
            />
        );
        expect(screen.getByText('Revenue Breakdown')).toBeInTheDocument();
    });

    it('renders Cost Breakdown table', () => {
        render(
            <ProfitAndLoss
                spConfigData={mockSrvcConfigData}
                userCustFields={mockUserCustFields}
                assigneeDetails={mockAssigneeDetails}
                roleList={mockRoleList}
                srvcReqData={mockSrvcReqData}
                orgNickName="test-org"
                srvcTypeId="test-type"
                srvcReqId="test-req"
            />
        );

        expect(screen.getByText('Cost Breakdown')).toBeInTheDocument();
    });

    it('should display the data in the request details section', () => {
        render(
            <ProfitAndLoss
                spConfigData={mockSrvcConfigData}
                userCustFields={mockUserCustFields}
                srvcReqData={mockSrvcReqData}
                locGrpsName="Test Location Group"
                assigneeDetails={mockAssigneeDetails}
                roleList={mockRoleList}
                orgNickName="test-org"
                srvcTypeId="test-type"
                srvcReqId="test-req"
            />
        );

        expect(screen.getByText('Vertical')).toBeInTheDocument();
        expect(screen.getByText('Location Group')).toBeInTheDocument();
        expect(screen.getByText('Order Date')).toBeInTheDocument();
        expect(screen.getByText('Completion Date')).toBeInTheDocument();
        expect(screen.getByText('Test Vertical')).toBeInTheDocument();
        expect(screen.getByText('Test Location Group')).toBeInTheDocument();
    });

    it('should display the authorities data in authorities section', () => {
        const mockAllAuthoritiesOfVertical = [
            { key: 'user1', label: 'Administrator' },
            { key: 'user2', label: 'Manager' },
        ];

        const mockSrvcReqDataWithAuth = {
            ...mockSrvcReqData,
            user1: 'user_1',
            user2: 'user_2',
        };

        render(
            <ProfitAndLoss
                srvcReqData={mockSrvcReqDataWithAuth}
                userCustFields={mockUserCustFields}
                allAuthoritiesOfVertical={mockAllAuthoritiesOfVertical}
                assigneeDetails={mockAssigneeDetails}
                roleList={mockRoleList}
                spConfigData={mockSrvcConfigData}
                orgNickName="test-org"
                srvcTypeId="test-type"
                srvcReqId="test-req"
            />
        );

        expect(screen.getByText('Authorities')).toBeInTheDocument();
    });

    it('should display alert info when no authorities are present', () => {
        const mockAllAuthoritiesOfVertical = [
            { key: 'user1', label: 'Administrator' },
            { key: 'user2', label: 'Manager' },
        ];

        render(
            <ProfitAndLoss
                spConfigData={mockSrvcConfigData}
                srvcReqData={{}}
                userCustFields={mockUserCustFields}
                allAuthoritiesOfVertical={mockAllAuthoritiesOfVertical}
                assigneeDetails={mockAssigneeDetails}
                roleList={mockRoleList}
                orgNickName="test-org"
                srvcTypeId="test-type"
                srvcReqId="test-req"
            />
        );

        expect(
            screen.getByText('No Authorities selected for this request')
        ).toBeInTheDocument();
    });

    it('should display (-) when net gm and total revenue fields are null.', () => {
        render(
            <ProfitAndLoss
                spConfigData={mockSrvcConfigData}
                userCustFields={mockUserCustFields}
                assigneeDetails={mockAssigneeDetails}
                roleList={mockRoleList}
                srvcReqData={mockSrvcReqData}
                orgNickName="test-org"
                srvcTypeId="test-type"
                srvcReqId="test-req"
            />
        );

        // check (-) for null or empty data
        expect(screen.getByText('Net GM')).toBeInTheDocument();
        const emptyData = screen.getAllByText('-');
        expect(emptyData[0]).toBeInTheDocument();
        expect(screen.getByText('Total Revenue')).toBeInTheDocument();
        expect(emptyData[1]).toBeInTheDocument();
    });
});

describe('ProfitAndLoss Component - Revenue Breakdown', () => {
    beforeEach(() => {
        jest.resetModules();
        jest.mock('../../util/http_utils', () => ({
            performGetCall: (url, params, onComplete) => {
                onComplete({
                    data: mockApiResp2,
                });
            },
            decodeErrorToMessage: (error) => error,
        }));
    });

    const newMockConfigData = {
        ...mockSrvcConfigData,
        sp_additional_revenue_field: 'f64fcd96-4a5e-413e-a8d8-821c2f692245',
    };

    it('renders component with addtional revenue rows', async () => {
        render(
            <ProfitAndLoss
                spConfigData={newMockConfigData}
                userCustFields={mockUserCustFields}
                assigneeDetails={mockAssigneeDetails}
                roleList={mockRoleList}
                srvcReqData={mockSrvcReqData}
                orgNickName="test-org"
                srvcTypeId="test-type"
                srvcReqId="test-req"
            />
        );

        expect(screen.getAllByText('Total Revenue')).toHaveLength(2);
        expect(screen.getByText('Additional Revenue')).toBeInTheDocument();
    });
});
