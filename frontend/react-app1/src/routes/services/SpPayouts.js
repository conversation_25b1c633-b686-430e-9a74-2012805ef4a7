import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>lapse, Modal, Tooltip } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { decodeFieldsMetaFrmJson } from '../../components/wify-utils/FieldCreator/helpers';
import MetaInputTable from '../../components/wify-utils/MetaInputTable';
import {
    convertDateFieldsToMoments,
    convertMomentToLocalDateString,
    convertUTCToDisplayTime,
    downloadExcelFileWithTabwise,
    getValueDataFrmFormMeta,
    isMobileView,
    parseFormulaToValue,
} from '../../util/helpers';
import _, { forEach } from 'lodash';
import LineItemConfigSummary from '../../components/WIFY/LineItemConfigSummary';
import ConfigHelpers from '../../util/ConfigHelpers';
import SpPayoutConfigSummary from '../../components/WIFY/SpPayoutConfigSummary';
import CountUp from '../../components/wify-utils/CountUp/countUp';

const numOr0 = (n) => (!n || isNaN(n) ? 0 : n);

const SpPayouts = ({
    config,
    spPayoutsData,
    onChange,
    readOnly,
    srvcConfigData,
    isBrand,
    isSrvcReqLock = false,
    srvc_type_id,
    revisionsData,
    title,
    heading,
    downloadLineItemData,
    vendorUsers,
    lineItemQTY,
    totalRevenue,
}) => {
    const [reRenderLineItems, setReRenderLineItems] = useState(false);
    const form_data = spPayoutsData?.form_data || {};
    const lineItemDataFrDownload = downloadLineItemData?.form_data || {};
    const [renderHelper, setRenderHelper] = useState(false);

    useEffect(() => {
        setRenderHelper(!renderHelper);
    }, [spPayoutsData]);

    const getColMeta = (lineItemGroup) => {
        const fields = decodeFieldsMetaFrmJson(lineItemGroup.fields) || [];
        const configFrGrp = config[lineItemGroup.key];
        const { quantity_field_formula } = configFrGrp;
        let lineItemMstPriceFromSrvcConfig = getLineItemMstPriceFormSrvcConfig(
            lineItemGroup.key,
            srvcConfigData
        );
        const staticColumns = [
            {
                key: 'qty',
                label: lineItemGroup.quantity_field_label || 'Quantity',
                widget: 'number',
                ...(quantity_field_formula?.length > 0
                    ? {
                          viewMode: true,
                          renderView: (text) => {
                              return (
                                  <div className="gx-px-2 gx-text-orange">
                                      <b>
                                          <CountUp
                                              end={text}
                                              duration={0.5}
                                              decimal="."
                                              decimals={2}
                                          />
                                      </b>
                                  </div>
                              );
                          },
                      }
                    : {}),
            },
            {
                key: 'rate',
                label: lineItemGroup.price_field_label || 'Price',
                widget: 'number',
                widgetProps: {
                    disabled:
                        lineItemMstPriceFromSrvcConfig &&
                        lineItemMstPriceFromSrvcConfig != '',
                },
            },
            {
                key: 'total',
                label: lineItemGroup.total_field_label || 'Total',
                widget: 'number',
                viewMode: true,
                renderView: (text) => {
                    return (
                        <div className="gx-px-2 gx-text-orange">
                            <b>
                                <CountUp
                                    end={text}
                                    duration={0.5}
                                    {...countUpParams}
                                />
                            </b>
                        </div>
                    );
                },
            },
        ];
        return [
            {
                key: 'vendor',
                label: 'Vendor',
                widget: 'select',
                widgetProps: {
                    style: {
                        width: '200px',
                    },
                },
                options: vendorUsers || [],
            },
            ...fields,
            ...staticColumns,
        ];
    };
    const getSpSrvcConfigData = () => {
        return srvcConfigData || [];
    };
    const getSpPriceConfigForSrvcTypeId = (
        srvcTypePriceConfig,
        lineItemGroupKey
    ) => {
        let prcConfigDatafrSingleSrvcType = [];
        let priceConfigFrSingleSrvcTypeId =
            srvcTypePriceConfig[lineItemGroupKey];
        if (
            priceConfigFrSingleSrvcTypeId &&
            priceConfigFrSingleSrvcTypeId.length > 0
        ) {
            prcConfigDatafrSingleSrvcType =
                priceConfigFrSingleSrvcTypeId.filter(
                    (singlePrcFrSrvcTypeId) =>
                        singlePrcFrSrvcTypeId.srvc_type_id == srvc_type_id
                )?.[0];
        } else {
            prcConfigDatafrSingleSrvcType = priceConfigFrSingleSrvcTypeId;
        }
        return prcConfigDatafrSingleSrvcType;
    };

    const getLineItemMstPriceFormSrvcConfig = (
        lineItemGroupKey,
        srvcConfigData
    ) => {
        let lineItemMasterPrice = '';
        let srvcTypePriceConfig = !isBrand
            ? getSpSrvcConfigData()?.srvc_type_pricing_config_for_line_item
            : srvcConfigData?.srvc_type_pricing_config_for_line_item;
        srvcTypePriceConfig =
            srvcTypePriceConfig ||
            srvcConfigData?.srvc_type_pricing_config_for_line_item;
        if (srvcTypePriceConfig) {
            srvcTypePriceConfig = JSON.parse(srvcTypePriceConfig);
            let lineItemPriceConfigFrGrp = !isBrand
                ? getSpPriceConfigForSrvcTypeId(
                      srvcTypePriceConfig,
                      lineItemGroupKey
                  )
                : srvcTypePriceConfig[lineItemGroupKey];
            if (
                lineItemPriceConfigFrGrp &&
                lineItemPriceConfigFrGrp.length > 0
            ) {
                let lineItemPriceConfigkey =
                    'line_item_' + lineItemGroupKey + '_master_rate';
                lineItemMasterPrice =
                    lineItemPriceConfigFrGrp?.[lineItemPriceConfigkey];
            } else {
                if (isBrand) {
                    let lineItemPriceConfigFrGrp =
                        srvcTypePriceConfig[lineItemGroupKey];
                    let lineItemPriceConfigkey =
                        'line_item_' + lineItemGroupKey + '_master_rate';
                    lineItemMasterPrice =
                        lineItemPriceConfigFrGrp?.[lineItemPriceConfigkey];
                } else {
                    let lineItemPriceConfigkey =
                        'line_item_' + lineItemGroupKey + '_master_rate';
                    lineItemMasterPrice =
                        lineItemPriceConfigFrGrp?.[lineItemPriceConfigkey];
                }
            }
        }
        return lineItemMasterPrice;
    };

    const getLineItemSelectPriceFormSrvcConfig = (
        lineItemGroup,
        singleLineItem
    ) => {
        const fields = decodeFieldsMetaFrmJson(lineItemGroup?.fields) || [];
        let lineItemSelectPrice = '';
        let priceList = [];
        let masterPrice = getLineItemMstPriceFormSrvcConfig(
            lineItemGroup?.key,
            srvcConfigData
        );
        if (masterPrice) {
            priceList.push(masterPrice);
        }
        let srvcTypePriceConfig = !isBrand
            ? getSpSrvcConfigData()?.srvc_type_pricing_config_for_line_item
            : srvcConfigData?.srvc_type_pricing_config_for_line_item;

        if (srvcTypePriceConfig && fields && fields.length > 0) {
            fields.forEach((singleField) => {
                let selectedSelectKey = singleLineItem[singleField.key];
                if (selectedSelectKey) {
                    if (typeof srvcTypePriceConfig != 'object') {
                        srvcTypePriceConfig = JSON.parse(srvcTypePriceConfig);
                    }
                    let lineItemPriceConfigFrGrp = !isBrand
                        ? getSpPriceConfigForSrvcTypeId(
                              srvcTypePriceConfig,
                              lineItemGroup?.key
                          )
                        : srvcTypePriceConfig[lineItemGroup?.key];

                    lineItemSelectPrice =
                        lineItemPriceConfigFrGrp?.[selectedSelectKey];
                    if (lineItemSelectPrice) {
                        priceList.push(lineItemSelectPrice);
                    }
                }
            });
        }
        // console.log("priceList",priceList)
        lineItemSelectPrice = getPriceBasedOnDeterminationEngineRule(
            lineItemGroup,
            priceList
        );
        return lineItemSelectPrice;
    };

    const getPriceBasedOnDeterminationEngineRule = (
        lineItemGroup,
        priceList
    ) => {
        let PriceBaseOnDeterminationEngineRule = '';
        let prc_config_determination_engine_key =
            'srvc_type_' +
            lineItemGroup?.key +
            '_pricing_config_determination_engine';
        if (srvcConfigData) {
            let prc_config_determination_engine_rule = !isBrand
                ? getSpSrvcConfigData()?.[prc_config_determination_engine_key]
                : srvcConfigData[prc_config_determination_engine_key];

            if (priceList && priceList.length > 0 && priceList != '') {
                if (prc_config_determination_engine_rule == 'lowest') {
                    PriceBaseOnDeterminationEngineRule = priceList?.reduce(
                        (singlepriceList, value) =>
                            Math.min(singlepriceList, value)
                    );
                } else if (prc_config_determination_engine_rule == 'highest') {
                    PriceBaseOnDeterminationEngineRule = priceList?.reduce(
                        (singlepriceList, value) =>
                            Math.max(singlepriceList, value)
                    );
                } else if (
                    prc_config_determination_engine_rule == 'aggregate'
                ) {
                    let total = priceList?.reduce(function (
                        singlepriceList,
                        value
                    ) {
                        return singlepriceList + value;
                    }, 0);
                    PriceBaseOnDeterminationEngineRule =
                        (total / priceList.length)?.toFixed(2) || 0;
                }
            }
        }
        return PriceBaseOnDeterminationEngineRule;
    };

    const getIdVsLabelMapping = (fields) => {
        const idVsLabelMapping = {};
        fields.forEach((singleFieldMeta) => {
            idVsLabelMapping[singleFieldMeta.label] = singleFieldMeta.key;
        });
        return idVsLabelMapping;
    };

    const getRowData = (lineItemGroup, isDownloadLineItemData = false) => {
        let lineItemsFrGrp = isDownloadLineItemData
            ? lineItemDataFrDownload[lineItemGroup.key] || [{}]
            : form_data[lineItemGroup.key] || [{}];
        const fields = getColMeta(lineItemGroup) || [];
        // decodeFieldsMetaFrmJson(lineItemGroup.fields) || [];
        if (lineItemsFrGrp) {
            lineItemsFrGrp.forEach((singleLineItemGrp) => {
                let singleGrpData = convertDateFieldsToMoments(
                    singleLineItemGrp,
                    fields
                );
                let srvcTypeCustomPriceconfig =
                    getLineItemSelectPriceFormSrvcConfig(
                        lineItemGroup,
                        singleGrpData
                    );
                if (
                    srvcTypeCustomPriceconfig &&
                    srvcTypeCustomPriceconfig != ''
                ) {
                    singleGrpData['rate'] = srvcTypeCustomPriceconfig;
                }

                const { quantity_field_formula, total_field_formula } =
                    config[lineItemGroup.key];
                if (
                    quantity_field_formula &&
                    quantity_field_formula.length > 0
                ) {
                    const qty = parseFormulaToValue(
                        quantity_field_formula,
                        getIdVsLabelMapping(fields),
                        singleGrpData,
                        !isSrvcReqLock ? false : undefined
                    );

                    if (!isNaN(qty)) {
                        singleGrpData.qty = qty;
                    }
                }
                if (total_field_formula && total_field_formula.length > 0) {
                    const total = parseFormulaToValue(
                        total_field_formula,
                        getIdVsLabelMapping(fields),
                        singleGrpData,
                        !isSrvcReqLock ? false : undefined
                    );
                    if (!isNaN(total)) {
                        singleGrpData.total = total;
                    }
                } else {
                    singleGrpData.total =
                        numOr0(singleGrpData.qty) * numOr0(singleGrpData.rate);
                }
            });

            let newspPayoutsData = { ...spPayoutsData };

            let form_data = newspPayoutsData?.form_data || {};
            newspPayoutsData['form_data'] = form_data;
            newspPayoutsData['total'] = getLineItemsTotal(newspPayoutsData);
            newspPayoutsData['total_qty'] =
                getLineItemsTotalQty(newspPayoutsData);
            let finalLineItemsSingleGroupWiseTotalQtyObj =
                getLineItemsSingleGroupWiseTotalQty(
                    newspPayoutsData,
                    lineItemGroup.key
                );
            newspPayoutsData = {
                ...newspPayoutsData,
                ...finalLineItemsSingleGroupWiseTotalQtyObj,
            };
            if (
                JSON.stringify(newspPayoutsData) !==
                    JSON.stringify(spPayoutsData) &&
                isSrvcReqLock == false
            ) {
                if (onChange) {
                    onChange(newspPayoutsData);
                }
            }
        }
        return lineItemsFrGrp;
    };

    const setNewRowData = (lineItemGroup, newData) => {
        // console.log('config',config);
        const fields = getColMeta(lineItemGroup) || [];
        // decodeFieldsMetaFrmJson(lineItemGroup.fields) || [];

        const configFrGrp = config[lineItemGroup.key];
        // Do Quantity and total calculations
        newData.forEach((singleLineItem) => {
            //rate autoifll based on srvc_config_price.
            //if rate is not define in price_config_master then blank and editable.
            //if rate is define then disable.
            let srvcTypeCustomPriceconfig =
                getLineItemSelectPriceFormSrvcConfig(
                    lineItemGroup,
                    singleLineItem
                );
            if (srvcTypeCustomPriceconfig) {
                singleLineItem.rate = srvcTypeCustomPriceconfig;
            }
            const { quantity_field_formula, total_field_formula } = configFrGrp;
            if (quantity_field_formula && quantity_field_formula.length > 0) {
                const qty = parseFormulaToValue(
                    quantity_field_formula,
                    getIdVsLabelMapping(fields),
                    singleLineItem,
                    false
                );
                singleLineItem.qty = qty;
            }
            if (total_field_formula && total_field_formula.length > 0) {
                const total = parseFormulaToValue(
                    total_field_formula,
                    getIdVsLabelMapping(fields),
                    singleLineItem,
                    !isSrvcReqLock ? false : undefined
                );
                if (!isNaN(total)) {
                    singleLineItem.total = total;
                }
            } else {
                singleLineItem.total =
                    numOr0(singleLineItem.qty) * numOr0(singleLineItem.rate);
            }
        });

        let newspPayoutsData = { ...spPayoutsData };
        let form_data = newspPayoutsData?.form_data || {};
        form_data[lineItemGroup.key] = newData;
        newspPayoutsData['form_data'] = form_data;
        newspPayoutsData['total'] = getLineItemsTotal(newspPayoutsData);
        newspPayoutsData['total_qty'] = getLineItemsTotalQty(newspPayoutsData);
        let finalLineItemsSingleGroupWiseTotalQtyObj =
            getLineItemsSingleGroupWiseTotalQty(
                newspPayoutsData,
                lineItemGroup.key
            );
        newspPayoutsData = {
            ...newspPayoutsData,
            ...finalLineItemsSingleGroupWiseTotalQtyObj,
        };
        if (onChange) {
            onChange(newspPayoutsData);
        }
    };

    const getLineItemsTotal = (spPayoutsData) => {
        let total = 0;
        Object.keys(spPayoutsData?.form_data).forEach((singleGroupId) => {
            const groupData = spPayoutsData.form_data[singleGroupId] || [];
            let grpTotal = 0;
            groupData.forEach((singleItem) => {
                grpTotal += parseFloat(numOr0(singleItem.total));
            });
            total += grpTotal;
        });
        return total;
    };

    const getLineItemsTotalQty = (spPayoutsData) => {
        let totalQty = 0;
        if (!_.isEmpty(spPayoutsData)) {
            Object.keys(spPayoutsData.form_data).map((singleGroupId) => {
                const groupData = spPayoutsData.form_data[singleGroupId] || [];
                let singleGrpTotalQty = 0;
                groupData.forEach((singleItem) => {
                    singleGrpTotalQty += parseFloat(singleItem.qty) || 0;
                });
                totalQty = totalQty + singleGrpTotalQty;
            });
        }
        return totalQty;
    };

    const getLineItemsSingleGroupWiseTotalQty = (
        spPayoutsData,
        lineItemGroupKey = undefined
    ) => {
        let lineItemsSingleGroupWiseTotalQtyObj = {};
        if (!_.isEmpty(spPayoutsData)) {
            Object.keys(spPayoutsData.form_data).map((singleGroupId) => {
                let key = `${singleGroupId}_total_qty`;
                if (
                    (lineItemGroupKey && lineItemGroupKey == singleGroupId) ||
                    !lineItemGroupKey
                ) {
                    const groupData =
                        spPayoutsData.form_data[singleGroupId] || [];
                    let singleGrpTotalQty = 0;
                    groupData.forEach((singleItem) => {
                        singleGrpTotalQty =
                            singleGrpTotalQty + numOr0(singleItem.qty);
                    });
                    lineItemsSingleGroupWiseTotalQtyObj[key] =
                        singleGrpTotalQty;
                }
            });
        }
        return { ...lineItemsSingleGroupWiseTotalQtyObj };
    };

    const getCommonTotalLabelFrConfigData = () => {
        return srvcConfigData?.srvc_type_line_item_common_qty_label || 'Total';
    };

    if (reRenderLineItems) {
        setTimeout(() => setReRenderLineItems(false), 100);
    }

    let countUpParams = {};
    if (!isSrvcReqLock) {
        countUpParams = {
            decimal: '.',
            decimals: 2,
        };
    }
    return (
        <>
            <div className="gx-mb-2 gx-d-flex wy-gap-20 gx-align-items-center">
                <div>
                    Line Item total quantity:{' '}
                    <b>{lineItemQTY?.toFixed(2) || 0}</b>
                </div>
                <div>
                    Revenue: <b>₹{totalRevenue || 0}</b>
                </div>
            </div>
            <div className="gx-my-4">
                <span>
                    Total Net Amount:
                    <b className="gx-ml-2 gx-text-orange">
                        ₹
                        <CountUp
                            end={spPayoutsData?.total}
                            duration={0.5}
                            {...countUpParams}
                        />
                    </b>
                </span>
            </div>
            {Object.keys(config).map((singleGroupId) => {
                const singleGroup = config[singleGroupId];
                return (
                    <div key={singleGroupId}>
                        {!reRenderLineItems && (
                            <MetaInputTable
                                edittable
                                colMeta={getColMeta(singleGroup)}
                                rowData={getRowData(singleGroup)}
                                noFilters
                                onChange={(newData) => {
                                    setNewRowData(singleGroup, newData);
                                }}
                                readOnly={readOnly || isSrvcReqLock}
                                actionColFixed={isMobileView() ? false : true}
                            />
                        )}
                        <div>
                            <SpPayoutConfigSummary groupConfig={singleGroup} />
                        </div>
                    </div>
                );
            })}
        </>
    );
};

export default SpPayouts;
