var express = require('express');
var router = express.Router();

var {
    getUserContextFrmReq,
    convertPasswordToHashInBody,
} = require('../api_models/utils/authrizor');

router.post('/export', function (req, res, next) {
    const user_model = setParamsToModel(req);
    user_model.exportUsersByEmail(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post('/change_user_password', function (req, res, next) {
    const user_model = setParamsToModel(req);
    user_model.changeUserPassword(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/overview_proto', function (req, res, next) {
    const user_model = setParamsToModel(req);
    user_model.getUserOverviewProto(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.put(
    '/:entry_id',
    convertPasswordToHashInBody,
    function (req, res, next) {
        const user_model = setParamsToModel(req);
        var entry_id = req.params.entry_id;
        console.log('entry_id', entry_id);
        user_model
            .createOrUpdateUser(req.body, entry_id)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.get('/proto/:entry_id', function (req, res, next) {
    const user_model = setParamsToModel(req);
    var entry_id = req.params.entry_id;
    user_model.getSingleEntry(req.query, entry_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post('/', convertPasswordToHashInBody, function (req, res, next) {
    // console.log("red body",req.body);
    // res.send("Got call");
    const user_model = setParamsToModel(req);
    if (req.body.batch_data) {
        user_model.createOrUpdateBatch(req.body).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
        return;
    } else {
        user_model.createOrUpdateUser(req.body).then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
    }
});

router.get('/', function (req, res, next) {
    const user_model = setParamsToModel(req);
    user_model.getAllUsers(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/proto', function (req, res, next) {
    const user_model = setParamsToModel(req);
    user_model.getViewDataFrUserForm(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/capacity', function (req, res, next) {
    const user_model = setParamsToModel(req);

    user_model.getCapacityConfiguration(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/:entry_id', function (req, res, next) {
    const user_model = setParamsToModel(req);
    var entry_id = req.params.entry_id;
    // console.log("reached entry_id is",entry_id);
    user_model
        .getUserDataById(req.query, entry_id, true)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.post('/update_fcm_token', function (req, res, next) {
    const user_model = setParamsToModel(req);
    user_model.updateFcmToken(req.body).then((resp) => {
        res.status(resp.httpStatus).send(resp.resp);
    });
});

router.get('/notification/list', function (req, res, next) {
    const user_model = setParamsToModel(req);
    // console.log("reached entry_id is",entry_id);
    user_model.getNotificationsFrUser(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.put('/notification/modify/:entry_id', function (req, res, next) {
    const user_model = setParamsToModel(req);
    // console.log("reached entry_id is",entry_id);
    user_model
        .updateNotificationsByUser(req.params.entry_id, req.body)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

const setParamsToModel = (req) => {
    const user_model = require('../api_models/user_model').getInstance();
    user_model.database = req.app.get('db');
    user_model.ip_addr = req.ip;
    user_model.user_agent = req.get('User-Agent');
    user_model.user_context = getUserContextFrmReq(req);
    return user_model.getFreshInstance(user_model);
};

module.exports = router;
