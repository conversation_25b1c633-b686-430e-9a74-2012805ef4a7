var express = require('express');
const { getUserContextFrmReq } = require('../../api_models/utils/authrizor');
var router = express.Router();

router.get('/overview-proto', function (req, res, next) {
    const availability_model = setParamsToModel(req);
    availability_model
        .getAvailabilityOverviewProto(req.query)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.get('/', function (req, res, next) {
    const availability_model = setParamsToModel(req);
    availability_model.getAllAvailability(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post('/export', function (req, res, next) {
    const availability_model = setParamsToModel(req);
    availability_model
        .exportAvailabilityByEmail(req.body)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

const setParamsToModel = (req) => {
    const availability_model =
        require('../../api_models/ace/availability_report_model').getInstance();
    availability_model.database = req.app.get('db');
    availability_model.databaseReplica = req.app.get('db_replica');
    availability_model.databaseDump = req.app.get('db_dump');
    availability_model.ip_addr = req.ip;
    availability_model.user_agent = req.get('User-Agent');
    availability_model.user_context = getUserContextFrmReq(req);
    return availability_model.getFreshInstance(availability_model);
};

module.exports = router;
