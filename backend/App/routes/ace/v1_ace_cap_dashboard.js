var express = require('express');
const { getUserContextFrmReq } = require('../../api_models/utils/authrizor');
var router = express.Router();

router.get('/overview-proto', function (req, res, next) {
    const model = require('../../api_models/ace/capacity_dashboard_model');
    setParamsToModel(req, model);
    model.getOverviewProto(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/state-wise', function (req, res, next) {
    const model = require('../../api_models/ace/capacity_dashboard_model');
    setParamsToModel(req, model);
    model.getStateWiseCapacity(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/pincodes-not-in-hubs', function (req, res, next) {
    const model = require('../../api_models/ace/capacity_dashboard_model');
    setParamsToModel(req, model);
    model.getPincodesNotInHubs(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post('/export-pincodes-not-in-hubs', function (req, res, next) {
    const model = require('../../api_models/ace/capacity_dashboard_model');
    setParamsToModel(req, model);

    // Merge query parameters with body
    const params = {
        ...req.body,
        vertical_id: req.query.vertical_id || req.body.vertical_id,
    };

    model.exportPincodesNotInHubs(params).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/orders-without-hub', function (req, res, next) {
    const model = require('../../api_models/ace/capacity_dashboard_model');
    setParamsToModel(req, model);
    model.getOrdersWithoutHub(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post('/export-orders-without-hubs', function (req, res, next) {
    console.log('exportOrdersWithoutHub', req.body);
    const model = require('../../api_models/ace/capacity_dashboard_model');
    setParamsToModel(req, model);

    // Merge query parameters with body
    const params = {
        ...req.body,
        vertical_id: req.query.vertical_id || req.body.vertical_id,
    };

    model.exportOrdersWithoutHub(params).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

const setParamsToModel = (req, model) => {
    model.database = req.app.get('db');
    model.ip_addr = req.ip;
    model.user_agent = req.get('User-Agent');
    model.user_context = getUserContextFrmReq(req);
};

module.exports = router;
