const sampleOperationResp = require('../utils/operationResp');
const db_resp = require('../utils/db_resp');
var HttpStatus = require('http-status-codes');
const users_model = require('../users_model');
const moment = require('moment');

class my_availability_model {
    validateAndFilterSlots(day, newSlots = [], previousSlots = []) {
        const now = moment();
        const previousMap = {};

        // Map previous slots by db_id
        for (const prev of previousSlots) {
            previousMap[prev.db_id] = prev;
        }

        const validSlots = [];

        for (const slot of newSlots) {
            const slotStart = moment(
                `${day} ${slot.start_time}`,
                'YYYY-MM-DD hh:mmA'
            );
            const isPast = slotStart.isBefore(now);
            const prevSlot = previousMap[slot.db_id];

            if (
                isPast &&
                prevSlot &&
                prevSlot.is_available === false &&
                slot.is_available === true
            ) {
                // Block this slot from update

                validSlots.push({ ...slot, is_available: false }); // revert change
            } else {
                validSlots.push(slot); // keep as-is
            }
        }

        return { validSlots };
    }

    getSlotsForTheDay(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                // console.log('query', JSON.stringify(query));
                let resp = (
                    await this.db.tms_ace_get_my_availability_proto(
                        JSON.stringify(query)
                    )
                )[0].tms_ace_get_my_availability_proto;
                if (resp.status === false) {
                    if (resp.code == 'capacity_details_missing') {
                        return resolve(
                            new sampleOperationResp(
                                false,
                                resp.code,
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    } else {
                        return resolve(
                            new sampleOperationResp(
                                false,
                                resp.data,
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                    }
                }
                // console.log('srvcReqs',JSON.stringify(srvcReqs));
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(resp.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('my_availability_model::getProto', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        'Unable to find slots',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    // TODO we will need complex mechanism to avoid
    // change in availability when someone already has
    // a subtask on him for that time
    createOrUpdateAvailability(query) {
        return new Promise(async (resolve, reject) => {
            try {
                const now = moment();
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                const day = query.day;
                const newSlots = query.slots || [];
                // Fetch existing slots
                const previousResp = await this.getSlotsForTheDay(query);
                let previousSlots = [];

                if (previousResp?.status && previousResp.data) {
                    previousSlots = JSON.parse(previousResp.data)?.slots || [];
                }

                // Run validation
                const { validSlots } = this.validateAndFilterSlots(
                    day,
                    newSlots,
                    previousSlots
                );

                query.slots = validSlots;

                const form_data = JSON.stringify(query);
                const resp = (
                    await this.db.tms_ace_create_or_update_my_availability(
                        form_data
                    )
                )[0].tms_ace_create_or_update_my_availability;
                // console.log('form_data ', JSON.stringify(query));
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(resp.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('createOrUpdateAvailability::post', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        'Internal Server Error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getSlotsForDateRange(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                let resp = (
                    await this.db.tms_ace_get_my_availability_fr_date_range(
                        JSON.stringify(query)
                    )
                )[0].tms_ace_get_my_availability_fr_date_range;
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(resp.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'my_availability_model::getSlotsForDateRange',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        'Unable to find slots',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }
}

module.exports = new my_availability_model();
