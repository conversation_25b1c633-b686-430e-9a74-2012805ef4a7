var sampleOperationResp = require('../utils/operationResp');
var HttpStatus = require('http-status-codes');
const users_model = require('../users_model');

const fs = require('fs');
const path = require('path');
const JSONStream = require('JSONStream');
const jsonToCsv = require('json-to-csv-stream');
const { allQueues } = require('../queues_v2/queues');
const { moduleKeys } = require('../utils/helper');
const {
    dumpExportsCounter,
    dumpExportSuccessCounter,
    dumpExportFailureCounter,
    dumpExportStatus,
} = require('../utils/metrics');

class capacity_dashboard_model {
    getOverviewProto(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                let respData = (
                    await this.db.tms_get_ace_capacity_dashboard_overview_proto(
                        JSON.stringify(query)
                    )
                )[0].tms_get_ace_capacity_dashboard_overview_proto;
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(respData.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('capacity_dashboard_model::getProto', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Unable to load',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getStateWiseCapacity(query) {
        return new Promise(async (resolve, reject) => {
            try {
                console.log(
                    'capacity_dashboard_model::getStateWiseCapacity :: STEP 1/2 - Initializing'
                );

                // Add organization ID and user ID to the query
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                // Call the database function to get state-wise capacity data
                let respData = (
                    await this.db.tms_get_ace_capacity_dash_state_wise_data(
                        JSON.stringify(query)
                    )
                )[0].tms_get_ace_capacity_dash_state_wise_data;

                console.log(
                    'capacity_dashboard_model::getStateWiseCapacity :: STEP 2/2 - Data retrieved'
                );

                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(respData.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'capacity_dashboard_model::getStateWiseCapacity',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message ||
                            'Unable to load state-wise capacity data',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getPincodesNotInHubs(query) {
        return new Promise(async (resolve, reject) => {
            try {
                console.log(
                    'capacity_dashboard_model::getPincodesNotInHubs :: STEP 1/2 - Initializing'
                );

                // Add organization ID and user ID to the query
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                // Get vertical_id from query
                const vertical_id = query['vertical_id'];
                if (!vertical_id) {
                    throw new Error('Vertical ID is required');
                }

                // Call the database function to get pincodes not in service hubs
                let respData = (
                    await this.db.tms_get_ace_capacity_dash_pincodes_not_in_hubs(
                        JSON.stringify(query)
                    )
                )[0].tms_get_ace_capacity_dash_pincodes_not_in_hubs;

                if (!respData.status) {
                    throw new Error(
                        respData.code || 'Failed to get pincodes data'
                    );
                }

                // Format the data for the frontend
                const formattedData = {
                    pincodes_not_in_hubs: respData.data || [],
                };

                console.log(
                    'capacity_dashboard_model::getPincodesNotInHubs :: STEP 2/2 - Data retrieved'
                );

                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(formattedData),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'capacity_dashboard_model::getPincodesNotInHubs',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message ||
                            'Unable to load pincodes not in hubs data',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    exportPincodesNotInHubs(query) {
        return new Promise(async (resolve, reject) => {
            try {
                console.log(
                    'capacity_dashboard_model::exportPincodesNotInHubs :: STEP 1/2 - Initializing'
                );

                // Add organization ID and user ID to the query
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                // Create a job for exporting pincodes not in hubs
                const jobData = { requester: query };
                allQueues.WIFY_PINCODES_NOT_IN_HUBS_EXPORT.addJob(jobData);

                console.log(
                    'capacity_dashboard_model::exportPincodesNotInHubs :: STEP 2/2 - Added to export queue'
                );

                resolve(
                    new sampleOperationResp(
                        true,
                        'Export request added to queue',
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'capacity_dashboard_model::exportPincodesNotInHubs',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message ||
                            'Unable to export pincodes not in hubs data',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    processPincodesNotInHubsExport(jobData) {
        return new Promise(async (resolve, reject) => {
            console.log(
                'capacity_export_model::processPincodesNotInHubsExport :: STEP 1/3 - Initializing'
            );
            let org_id = jobData?.requester?.org_id;
            let vertical_id = jobData?.requester?.vertical_id;
            // Prepare query for database function
            const query = { ...jobData.requester, is_dump: '1' };

            console.log(
                'capacity_export_model::processPincodesNotInHubsExport :: STEP 2/3 - Fetching data'
            );

            const dbObj = this.dbReplica || this.db;

            dbObj
                .tms_get_ace_capacity_dash_pincodes_not_in_hubs(
                    JSON.stringify(query),
                    {
                        stream: true,
                    }
                )
                .then(
                    (stream) => {
                        // we need to start streaming the incoming data
                        // and save to temp folder
                        // once saved trigger email
                        // let org_id = jobData?.requester?.org_id;
                        const d = new Date(); // today now
                        let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                        let savePath = path.join(
                            '',
                            'temp_files',
                            'pincodes_not_in_hubs',
                            '' + org_id,
                            today
                        );
                        fs.mkdir(savePath, { recursive: true }, (err) => {
                            if (err) {
                                if (err.code != 'EEXIST') {
                                    return console.log(
                                        'Error in temp folder creation',
                                        err
                                    );
                                }
                            }

                            // Create file path
                            const fileName = `Pincodes_Not_In_Hubs_${today}.csv`;
                            const filePath = path.join(savePath, fileName);

                            stream.on('end', () => {
                                // do something with the created file
                                console.log(
                                    'capacity_export_model::processPincodesNotInHubsExport :: STEP 3/3 - Sending email'
                                );

                                //Send email by QUEUE
                                let to = jobData.requester?.email_id;
                                let subject = jobData.requester?.subject;
                                let message =
                                    '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                let attachments = [
                                    { path: filePath, filename: fileName },
                                ];

                                //optinal param for save eamil_log
                                let usr_id = jobData?.requester?.usr_id;
                                let ip_address = jobData?.requester?.ip_addr;
                                let user_agent = jobData?.requester?.user_agent;

                                const emailJobData = {
                                    to,
                                    subject,
                                    message,
                                    attachments,
                                    org_id,
                                    usr_id,
                                    ip_address,
                                    user_agent,
                                };
                                allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
                                dumpExportSuccessCounter.inc({
                                    module: moduleKeys.unallocatedPincode,
                                });
                                dumpExportsCounter.inc({
                                    status: dumpExportStatus.success,
                                });
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        'Added to email queue',
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            });
                            stream
                                .pipe(JSONStream.stringify())
                                .pipe(
                                    jsonToCsv({
                                        path: '*.tms_get_ace_capacity_dash_pincodes_not_in_hubs',
                                    })
                                )
                                .pipe(fs.createWriteStream(filePath));
                        });
                    },
                    (err) => {
                        dumpExportFailureCounter.inc({
                            module: moduleKeys.pincodesNotInHubs,
                        });
                        dumpExportsCounter.inc({
                            status: dumpExportStatus.failure,
                        });
                        this.fatalDbError(resolve, err);
                    }
                );
        });
    }
    getOrdersWithoutHub(query) {
        return new Promise(async (resolve, reject) => {
            try {
                console.log(
                    'capacity_dashboard_model::getOrdersWithoutHub :: STEP 1/2 - Initializing'
                );

                // Add organization ID and user ID to the query
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                // Call the database function to get orders without hub data
                let respData = (
                    await this.db.tms_get_ace_capacity_dash_orders_without_hub(
                        JSON.stringify(query)
                    )
                )[0].tms_get_ace_capacity_dash_orders_without_hub;

                if (!respData.status) {
                    throw new Error(
                        respData.code || 'Failed to get orders without hub data'
                    );
                }

                // Format the data for the frontend
                const formattedData = {
                    orders_without_hub: respData.data || [],
                };

                console.log(
                    'capacity_dashboard_model::getOrdersWithoutHub :: STEP 2/2 - Data retrieved'
                );

                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(formattedData),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'capacity_dashboard_model::getOrdersWithoutHub',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message ||
                            'Unable to load orders without hub data',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }
    exportOrdersWithoutHub(query) {
        return new Promise(async (resolve, reject) => {
            try {
                console.log(
                    'capacity_dashboard_model::exportOrdersWithoutHub :: STEP 1/2 - Initializing'
                );

                // Add organization ID and user ID to the query
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;

                // Create a job for exporting orders without hub
                const jobData = { requester: query };
                allQueues.WIFY_ORDERS_WITHOUT_HUB_EXPORT.addJob(jobData);
                console.log(
                    'capacity_dashboard_model::exportOrdersWithoutHub :: STEP 2/2 - Added to export queue',
                    jobData
                );
                console.log(
                    'capacity_dashboard_model::exportOrdersWithoutHub :: STEP 2/2 - Added to export queue'
                );

                resolve(
                    new sampleOperationResp(
                        true,
                        'Export request added to queue',
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'capacity_dashboard_model::exportOrdersWithoutHub',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message ||
                            'Unable to export orders without hub data',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }
    processOrdersWithoutHubExport(jobData) {
        return new Promise(async (resolve, reject) => {
            console.log(
                'capacity_export_model::processOrdersWithoutHubExport :: STEP 1/3 - Initializing'
            );
            let org_id = jobData?.requester?.org_id;
            let vertical_id = jobData?.requester?.vertical_id;
            // Prepare query for database function
            const query = { ...jobData.requester, is_dump: '1' };

            console.log(
                'capacity_export_model::processOrdersWithoutHubExport :: STEP 2/3 - Fetching data',
                JSON.stringify(query)
            );

            const dbObj = this.dbReplica || this.db;

            dbObj
                .tms_get_ace_capacity_dash_orders_without_hub_dump(
                    JSON.stringify(query),
                    {
                        stream: true,
                    }
                )
                .then(
                    (stream) => {
                        // we need to start streaming the incoming data
                        // and save to temp folder
                        // once saved trigger email
                        // let org_id = jobData?.requester?.org_id;
                        const d = new Date(); // today now
                        let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                        let savePath = path.join(
                            '',
                            'temp_files',
                            'orders_without_hub',
                            '' + org_id,
                            today
                        );
                        fs.mkdir(savePath, { recursive: true }, (err) => {
                            if (err) {
                                if (err.code != 'EEXIST') {
                                    return console.log(
                                        'Error in temp folder creation',
                                        err
                                    );
                                }
                            }

                            // Create file path
                            const fileName = `Orders_Without_Hub_${today}.csv`;
                            const filePath = path.join(savePath, fileName);

                            stream.on('end', () => {
                                // do something with the created file
                                console.log(
                                    'capacity_export_model::processOrdersWithoutHubExport :: STEP 3/3 - Sending email'
                                );

                                //Send email by QUEUE
                                let to = jobData.requester?.email_id;
                                let subject = jobData.requester?.subject;
                                let message =
                                    '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                let attachments = [
                                    { path: filePath, filename: fileName },
                                ];

                                //optinal param for save eamil_log
                                let usr_id = jobData?.requester?.usr_id;
                                let ip_address = jobData?.requester?.ip_addr;
                                let user_agent = jobData?.requester?.user_agent;

                                const emailJobData = {
                                    to,
                                    subject,
                                    message,
                                    attachments,
                                    org_id,
                                    usr_id,
                                    ip_address,
                                    user_agent,
                                };
                                allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
                                dumpExportSuccessCounter.inc({
                                    module: moduleKeys.unallocatedPincode,
                                });
                                dumpExportsCounter.inc({
                                    status: dumpExportStatus.success,
                                });
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        'Added to email queue',
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                            });
                            stream
                                .pipe(JSONStream.stringify())
                                .pipe(
                                    jsonToCsv({
                                        path: '*.tms_get_ace_capacity_dash_orders_without_hub_dump',
                                    })
                                )
                                .pipe(fs.createWriteStream(filePath));
                        });
                    },
                    (err) => {
                        dumpExportFailureCounter.inc({
                            module: moduleKeys.pincodesNotInHubs,
                        });
                        dumpExportsCounter.inc({
                            status: dumpExportStatus.failure,
                        });
                        this.fatalDbError(resolve, err);
                    }
                );
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }
}

module.exports = new capacity_dashboard_model();
