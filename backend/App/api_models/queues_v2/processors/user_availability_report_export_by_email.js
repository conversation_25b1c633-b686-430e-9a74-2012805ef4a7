const {
    getAvailabilityReportModelFrQueue,
} = require('./helpers/availability_report_helper');

const timeoutThreshold = 20; //seconds

const startTimeChecker = (spentTimeObj, done) => {
    setTimeout(() => {
        console.log('export spent time (secs)', spentTimeObj);
        // Cancel timeout if work was done within specific time
        if (spentTimeObj.spentTime > timeoutThreshold) {
            console.log('export request timedout, completing job forcefully');
            spentTimeObj.spentTime = -1;
            done(null, {});
        }
        if (spentTimeObj.spentTime >= 0) {
            spentTimeObj.spentTime = spentTimeObj.spentTime + 1;
            startTimeChecker(spentTimeObj, done);
        }
    }, 1000);
};

const performJob = async (job, done) => {
    const app = require('../../../app');
    const availabilityReportModel = getAvailabilityReportModelFrQueue(
        app,
        job.data.requester
    );
    let spentTimeObj = { spentTime: 0 };
    startTimeChecker(spentTimeObj, done);
    const resp = await availabilityReportModel.processAvailabilityExportByEmail(
        job.data
    );
    console.log('CapacityWiseAvailabilityExportByEmail Resp ', resp);
    spentTimeObj.spentTime = -1;
    done(null, {});
};

exports.default = performJob;
