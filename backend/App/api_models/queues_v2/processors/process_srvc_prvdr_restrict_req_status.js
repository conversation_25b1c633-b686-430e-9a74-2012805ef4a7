const { getSrvcTypeModelFrQueue } = require('./helpers/service_type_helper');
const app = require('../../../app');

const performJob = async (job, done) => {
    console.log(
        'performJob :: process_srvc_prvdr_restrict_req_status',
        job.data
    );
    const {
        query,
        entry_id,
        service_type_model_data: srvcTypeModelData,
    } = job.data;

    // Create service_type_model
    const model = getSrvcTypeModelFrQueue(app, srvcTypeModelData);
    let resp = await model.createOrUpdateIsRestrictOnSrvcReq(query, entry_id);
    console.log('process_srvc_prvdr_restrict_req_status resp ::', resp);
    done(null, { status: true });
};

exports.default = performJob;
