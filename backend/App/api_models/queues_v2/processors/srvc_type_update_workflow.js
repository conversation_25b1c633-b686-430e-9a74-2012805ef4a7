const service_type_workflow = require('../../workflows/service_type_workflow');
const { getSrvcTypeModelFrQueue } = require('./helpers/service_type_helper');
const {
    getSrvcTypeWorkflowModel,
} = require('./helpers/srvc_type_workflow_helper');

const performJob = async (job, done) => {
    const app = require('../../../app');
    const mainDb = app.get('db');

    const model = getSrvcTypeModelFrQueue(
        app,
        job.data.service_type_model_data
    );
    let workflow = getSrvcTypeWorkflowModel(model);
    let { query, entry_id } = job.data;
    await workflow.processUpdateWorkFlow(query, entry_id, mainDb);
    done(null, {});
};

exports.default = performJob;
