/**
 * Utility functions for capacity update operations
 */
const { callLambdaWithApiGatewayEvent } = require('../utils/lambda_helpers');

/**
 * Transforms a capacity record from the database format to the API format
 * @param {Object} record - The capacity record from the database
 * @param {string} orgId - The organization ID
 * @param {string} orgName - The organization name
 * @returns {Object} - The transformed capacity record
 */
function transformCapacityRecord(record, orgId, orgName) {
    // Extract the necessary fields from the record
    // Adjust the field names based on your actual database schema
    return {
        resourceId: record.resource_id || '',
        startTime: record.start_time || '',
        endTime: record.end_time || '',
        totalCapacity: parseInt(record.total_capacity || 0, 10),
        availableCapacity: parseInt(record.available_capacity || 0, 10),
        bookedCapacity: parseInt(record.booked_capacity || 0, 10),
        organizationId: orgId,
        organizationName: orgName,
        metadata: {
            verticalId: record.vertical_id || '',
            verticalName: record.vertical_name || '',
            skillId: record.skill_id || '',
            skillName: record.skill_name || '',
            hubId: record.hub_id || '',
            hubName: record.hub_name || '',
            hubCode: record.hub_code || '',
            providerId: record.provider_id || '',
        },
    };
}

/**
 * Generate test capacity records for performance testing
 * @param {number} count - Number of records to generate
 * @returns {Array} - Array of test capacity records
 */
function generateTestCapacityRecords(count) {
    const records = [];
    const startDate = new Date();
    startDate.setHours(0, 0, 0, 0); // Start at midnight today

    // Generate a mix of resources, verticals, skills, and hubs
    const providers = ['provider1', 'provider2', 'provider3'];
    const verticals = Array.from({ length: 10 }, (_, i) => `vertical${i + 1}`);
    const skills = Array.from({ length: 20 }, (_, i) => `skill${i + 1}`);
    const hubs = Array.from({ length: 100 }, (_, i) => `hub${i + 1}`);

    // Generate records with different time slots throughout the day
    for (let i = 0; i < count; i++) {
        // Cycle through different combinations of resources
        const providerId = providers[i % providers.length];
        const verticalId = verticals[i % verticals.length];
        const skillId = skills[i % skills.length];
        const hubId = hubs[i % hubs.length];

        // Create a resource ID combining all components
        const resourceId = `${providerId}_${verticalId}_${skillId}_${hubId}`;

        // Create time slots (30-minute intervals)
        const slotIndex = Math.floor(i / (count / 48)) % 48; // 48 half-hour slots in a day
        const startTime = new Date(startDate);
        startTime.setMinutes(slotIndex * 30);

        const endTime = new Date(startTime);
        endTime.setMinutes(startTime.getMinutes() + 30);

        // Random capacity values
        const totalCapacity = Math.floor(Math.random() * 20) + 5; // 5-25
        const availableCapacity = Math.floor(
            Math.random() * (totalCapacity + 1)
        ); // 0-totalCapacity
        const bookedCapacity = totalCapacity - availableCapacity;

        records.push({
            resource_id: resourceId,
            start_time: startTime.toISOString(),
            end_time: endTime.toISOString(),
            total_capacity: totalCapacity,
            available_capacity: availableCapacity,
            booked_capacity: bookedCapacity,
            vertical_id: verticalId,
            skill_id: skillId,
            hub_id: hubId,
            provider_id: providerId,
        });
    }

    return records;
}

/**
 * Call Lambda with batch data
 * @param {Array} transformedBatch - The transformed batch data
 * @param {string} orgId - The organization ID
 * @param {string} orgName - The organization name
 * @param {string} lambdaARN - The Lambda ARN to call
 * @returns {Promise<Object>} - Lambda response
 */
async function callLambdaWithBatchData(
    transformedBatch,
    orgId,
    orgName,
    lambdaARN
) {
    const endpoint = '/capacity/batch';
    const options = {
        FunctionName: lambdaARN,
        endpoint: endpoint,
        method: 'POST',
        data: {
            capacityRecords: transformedBatch,
            organizationId: orgId,
            organizationName: orgName,
        },
        queryParams: {}, // No query parameters
        headers: {
            'x-api-key':
                process.env.BOOKINGS_SERVICE_API_KEY ||
                'tms-bookings-service-dev-key-123456', // Fallback to default dev key
            'Content-Type': 'application/json',
            Accept: 'application/json',
        },
    };

    return await callLambdaWithApiGatewayEvent(options);
}

/**
 * Parse Lambda response
 * @param {Object} lambdaResponse - The Lambda response
 * @returns {Object} - Parsed response data
 */
function parseLambdaResponse(lambdaResponse) {
    try {
        const rawPayload = lambdaResponse.Payload;

        // Parse the payload if it's a string
        if (typeof rawPayload === 'string') {
            const parsedPayload = JSON.parse(rawPayload);

            // Check for validation errors in the response
            if (
                parsedPayload &&
                parsedPayload.message &&
                parsedPayload.message.includes('validation failed with')
            ) {
                console.error(
                    'Validation error detected:',
                    parsedPayload.message
                );
                console.error('Full response:', JSON.stringify(parsedPayload));
            }

            return parsedPayload;
        } else {
            return rawPayload;
        }
    } catch (parseError) {
        console.error(
            'Error encountered while parsing Lambda response:',
            parseError
        );

        // Check if the response is HTML (common error case)
        const rawPayload = lambdaResponse.Payload;
        const isHtml =
            typeof rawPayload === 'string' &&
            (rawPayload.startsWith('<!DOCTYPE') ||
                rawPayload.startsWith('<html') ||
                rawPayload.startsWith('<?xml') ||
                rawPayload.startsWith('<'));

        if (isHtml) {
            console.error(
                'Invalid response format: Received HTML instead of expected JSON response'
            );
            return {
                success: false,
                message:
                    'Invalid response format: Received HTML instead of expected JSON response',
                errorType: 'HTML_RESPONSE',
            };
        } else {
            // For other parsing errors
            return {
                success: false,
                message:
                    'Failed to parse Lambda response: ' + parseError.message,
                errorType: 'PARSE_ERROR',
            };
        }
    }
}

/**
 * Evaluate response success
 * @param {Object} responseData - The parsed response data
 * @param {number} statusCode - The HTTP status code
 * @returns {Object} - Result object with success/failure determination
 */
function evaluateResponseSuccess(responseData, statusCode) {
    // 1. Check HTTP status code is in 2xx range
    const httpSuccess = statusCode >= 200 && statusCode < 300;

    // 2. Check if the response body indicates success
    const responseSuccess =
        responseData &&
        (responseData.success === true ||
            responseData.status === true ||
            (typeof responseData === 'object' &&
                !responseData.error &&
                !responseData.errorMessage));

    // 3. Look for error indicators in the response
    const hasErrorMessage =
        responseData &&
        (responseData.error ||
            responseData.errorMessage ||
            (responseData.message &&
                typeof responseData.message === 'string' &&
                (responseData.message.toLowerCase().includes('error') ||
                    responseData.message.toLowerCase().includes('fail'))));

    // Create a result object with comprehensive success/failure detection
    return {
        success: httpSuccess && responseSuccess && !hasErrorMessage,
        resp: responseData,
        httpStatus: statusCode,
        errorDetails: hasErrorMessage
            ? {
                  error:
                      responseData.error ||
                      responseData.errorMessage ||
                      responseData.message,
                  code:
                      responseData.errorCode ||
                      responseData.code ||
                      'UNKNOWN_ERROR',
              }
            : null,
    };
}

/**
 * Create success response
 * @param {Object} result - The result object
 * @param {Object} responseData - The parsed response data
 * @param {Array} transformedBatch - The transformed batch data
 * @param {number} batchIndex - The batch index
 * @returns {Object} - Success response object
 */
function createSuccessResponse(
    result,
    responseData,
    transformedBatch,
    batchIndex
) {
    // If the batch was successful, count all records as successful
    const batchSuccessCount =
        responseData.data?.successCount || transformedBatch.length;
    const batchFailureCount = responseData.data?.failureCount || 0;

    // Extract resourceId from the first record in the batch
    const resourceId =
        transformedBatch.length > 0
            ? transformedBatch[0].resourceId
            : 'unknown';
    console.log(
        `${resourceId} | BATCH: ${batchIndex + 1} | SUCCESS: ${batchSuccessCount} | FAILED: ${batchFailureCount}`
    );

    // Return success result
    return {
        batchIndex: batchIndex + 1,
        recordCount: transformedBatch.length,
        success: true,
        successCount: batchSuccessCount,
        failureCount: batchFailureCount,
        message: 'Batch processed successfully',
        statusCode: result.httpStatus,
    };
}

/**
 * Create error response
 * @param {Object} result - The result object
 * @param {Array} transformedBatch - The transformed batch data
 * @param {number} batchIndex - The batch index
 * @returns {Object} - Error response object
 */
function createErrorResponse(result, transformedBatch, batchIndex) {
    // Extract resourceId from the first record in the batch
    const resourceId =
        transformedBatch.length > 0
            ? transformedBatch[0].resourceId
            : 'unknown';

    const errorMessage = result.errorDetails?.error || 'Unknown error';

    // Add more detailed logging for validation errors
    if (errorMessage.includes('validation failed with')) {
        console.error(
            `${resourceId} | BATCH: ${batchIndex + 1} | ERROR: ${errorMessage}`
        );
        // Log the full response for debugging validation errors
        console.error(
            `${resourceId} | BATCH: ${batchIndex + 1} | RESPONSE: ${JSON.stringify(result.resp)}`
        );
    } else {
        console.error(
            `${resourceId} | BATCH: ${batchIndex + 1} | ERROR: ${errorMessage}`
        );
    }

    // Return failure result with retryable flag
    return {
        batchIndex: batchIndex + 1,
        recordCount: transformedBatch.length,
        success: false,
        message: `Capacity batch processing failed: ${errorMessage}`,
        statusCode: result.httpStatus,
        errorDetails: result.errorDetails,
        retryable: true, // Indicate this error can be retried
    };
}

module.exports = {
    transformCapacityRecord,
    generateTestCapacityRecords,
    callLambdaWithBatchData,
    parseLambdaResponse,
    evaluateResponseSuccess,
    createSuccessResponse,
    createErrorResponse,
};
