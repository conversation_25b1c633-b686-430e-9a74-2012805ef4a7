const { allQueues } = require('../queues_v2/queues');
const HttpStatus = require('http-status-codes');
const sampleOperationResp = require('../utils/operationResp');
const {
    transformCapacityRecord,
    generateTestCapacityRecords,
    callLambdaWithBatchData,
    parseLambdaResponse,
    evaluateResponseSuccess,
    createSuccessResponse,
    createErrorResponse,
} = require('./utils');

class CapacityUpdateModel {
    // Static flag to control processing mode (stream or direct)
    static USE_DIRECT_QUERY = false; // Set to true for direct query, false for streaming

    constructor() {
        this.db = null;
        this.databaseReplica = null;
        this.ip_address = null;
        this.user_agent_ = null;
    }

    /**
     * Initiates the capacity update process
     * @param {Object} query - Query parameters from the request
     * @returns {Promise} - Promise that resolves to a response object
     */
    initiateCapacityUpdate(query) {
        return new Promise(async (resolve) => {
            try {
                if (!this.db) {
                    console.error('Database connection not available');
                    resolve({
                        status: false,
                        message: 'Database connection not available',
                        timestamp: new Date().toISOString(),
                    });
                    return;
                }
                const dbResp = (
                    await this.db.tms_ace_get_capacity_enabled_orgs()
                )[0].tms_ace_get_capacity_enabled_orgs;

                // Check if capacity module is enabled for any organizations
                if (
                    !dbResp.status ||
                    !dbResp.data ||
                    !dbResp.data.enabled_organizations ||
                    !dbResp.data.enabled_organizations.length
                ) {
                    return resolve(
                        new sampleOperationResp(
                            false,
                            {
                                status: 'error',
                                message:
                                    'No organizations with capacity module enabled found',
                                timestamp: new Date().toISOString(),
                            },
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                }

                const enabledOrgs = dbResp.data.enabled_organizations;

                // Track jobs added
                const jobsAdded = [];

                // Loop through each organization and add a job to the queue
                for (const org of enabledOrgs) {
                    // Prepare job data for the queue
                    const jobData = {
                        query: query,
                        org_id: org.org_id,
                        org_name: org.org_name,
                        ip_address: this.ip_address,
                        user_agent: this.user_agent_,
                        timestamp: new Date().toISOString(),
                    };

                    // Add job to the queue
                    // Using our dedicated CRON_CAPACITY_UPDATE queue
                    allQueues.CRON_CAPACITY_UPDATE.addJob(jobData);

                    jobsAdded.push({
                        org_id: org.org_id,
                        org_name: org.org_name,
                    });
                }

                // Return success response
                resolve(
                    new sampleOperationResp(
                        true,
                        {
                            status: 'success',
                            message: `Capacity sync triggered for ${jobsAdded.length} organizations`,
                            jobs_added: jobsAdded,
                            timestamp: new Date().toISOString(),
                        },
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('Error in initiateCapacityUpdate:', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        {
                            status: 'error',
                            message:
                                error.message ||
                                'Failed to initiate capacity update',
                            timestamp: new Date().toISOString(),
                        },
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    /**
     * Process the capacity update
     * @param {Object} data - The job data from the queue
     * @returns {Promise} - Promise that resolves to a result object
     */
    processCapacityUpdate(data) {
        return new Promise(async (resolve) => {
            try {
                if (!this.db) {
                    console.error('Database connection not available');
                    resolve({
                        status: false,
                        message: 'Database connection not available',
                        timestamp: new Date().toISOString(),
                    });
                    return;
                }

                // Extract organization ID from job data
                const orgId = data.org_id;
                if (!orgId) {
                    console.error('Organization ID not provided in job data');
                    resolve({
                        status: false,
                        message: 'Organization ID not provided in job data',
                        timestamp: new Date().toISOString(),
                    });
                    return;
                }

                // Step 1: Get unique resource ID combinations
                try {
                    console.log(
                        `ORG_ID: ${orgId} | FETCH: resource_combinations`
                    );

                    // Call the new database function to get unique resource combinations
                    const resourceCombinations = await this.db.query(
                        'SELECT * FROM public.tms_ace_get_unique_resource_combinations($1)',
                        [orgId]
                    );

                    console.log(
                        `FOUND: ${resourceCombinations.length} | TYPE: resource_combinations`
                    );

                    // If no resource combinations were found, return an error
                    if (resourceCombinations.length === 0) {
                        console.error('No resource combinations found');
                        resolve({
                            status: false,
                            message: 'No resource combinations found',
                            timestamp: new Date().toISOString(),
                        });
                        return;
                    }

                    // Step 2: Create a background job for each resource combination
                    const jobsAdded = [];
                    let successCount = 0;
                    let failureCount = 0;

                    for (const resource of resourceCombinations) {
                        try {
                            // Prepare job data for the queue
                            const jobData = {
                                orgId: orgId,
                                orgName: data.org_name,
                                resourceId: resource.resource_id,
                                providerId: resource.provider_id,
                                verticalId: resource.vertical_id,
                                skillId: resource.skill_id,
                                hubId: resource.hub_id,
                                ip_address: this.ip_address,
                                user_agent: this.user_agent_,
                                timestamp: new Date().toISOString(),
                            };

                            // Add job to the dedicated resource update queue
                            allQueues.CRON_CAPACITY_RESOURCE_UPDATE.addJob(
                                jobData,
                                {
                                    attempts: 10, // Retry up to 10 times
                                    backoff: {
                                        type: 'exponential',
                                        delay: 5000, // Start with 5 seconds delay, then exponential backoff
                                    },
                                    removeOnComplete: true,
                                    removeOnFail: false, // Keep failed jobs for inspection
                                }
                            );

                            jobsAdded.push({
                                resourceId: resource.resource_id,
                                verticalName: resource.vertical_name,
                                skillName: resource.skill_name,
                                hubName: resource.hub_name,
                            });

                            successCount++;
                        } catch (queueError) {
                            console.error(
                                `Error adding resource job to queue for resource ID ${resource.resource_id}:`,
                                queueError
                            );
                            failureCount++;
                        }
                    }

                    // Return success with the job summary
                    resolve({
                        status: failureCount === 0,
                        message: `Capacity update jobs created for ${successCount} resource combinations`,
                        data: {
                            totalResources: resourceCombinations.length,
                            successCount,
                            failureCount,
                            sampleJobs: jobsAdded.slice(0, 5), // Include just a few sample jobs
                        },
                        timestamp: new Date().toISOString(),
                    });
                } catch (dbError) {
                    console.error(
                        'Error fetching resource combinations:',
                        dbError
                    );
                    resolve({
                        status: false,
                        message:
                            dbError.message ||
                            'Failed to fetch resource combinations',
                        timestamp: new Date().toISOString(),
                    });
                }
            } catch (error) {
                console.error('Error in processCapacityUpdate:', error);
                resolve({
                    status: false,
                    message:
                        error.message || 'Failed to process capacity update',
                    timestamp: new Date().toISOString(),
                });
            }
        });
    }

    /**
     * Process capacity data for a specific resource combination
     * @param {number} orgId - The organization ID
     * @param {string} orgName - The organization name
     * @param {string} resourceId - The resource ID
     * @param {number} providerId - The provider ID (not used directly but kept for consistency)
     * @param {number} verticalId - The vertical ID
     * @param {number} skillId - The skill ID
     * @param {number} hubId - The hub ID
     * @returns {Promise} - Promise that resolves to a result object
     */
    processResourceCapacityUpdate(
        orgId,
        orgName,
        resourceId,
        providerId, // Not used directly but kept for consistency with job data
        verticalId,
        skillId,
        hubId
    ) {
        return new Promise(async (resolve) => {
            try {
                if (!this.db) {
                    console.error('Database connection not available');
                    resolve({
                        status: false,
                        message: 'Database connection not available',
                        timestamp: new Date().toISOString(),
                    });
                    return;
                }

                console.log(
                    `${resourceId} | ORG_ID: ${orgId} | VERTICAL: ${verticalId} | SKILL: ${skillId} | HUB: ${hubId}`
                );

                // Fetch capacity data for this specific resource combination
                try {
                    const capacityData = await this.db.query(
                        'SELECT * FROM public.tms_ace_get_capacity_data_by_resource($1, $2, $3, $4)',
                        [orgId, verticalId, skillId, hubId]
                    );

                    console.log(
                        `${resourceId} | RECORDS: ${capacityData.length}`
                    );

                    // If no records were found, return an error
                    if (capacityData.length === 0) {
                        console.error(`${resourceId} | ERROR: no_records`);
                        resolve({
                            status: false,
                            message: `No capacity records found for resource ID: ${resourceId}`,
                            timestamp: new Date().toISOString(),
                        });
                        return;
                    }

                    // Process capacity data using database function instead of Lambda
                    const BATCH_SIZE = 5000; // Increased batch size for database operations
                    const totalBatches = Math.ceil(
                        capacityData.length / BATCH_SIZE
                    );
                    const results = [];
                    let successCount = 0;
                    let failureCount = 0;

                    console.log(
                        `${resourceId} | PROCESSING: ${capacityData.length} records in ${totalBatches} batches using database function`
                    );

                    // Process capacity data in batches using database function
                    for (
                        let batchIndex = 0;
                        batchIndex < totalBatches;
                        batchIndex++
                    ) {
                        const startIdx = batchIndex * BATCH_SIZE;
                        const endIdx = Math.min(
                            startIdx + BATCH_SIZE,
                            capacityData.length
                        );
                        const batchData = capacityData.slice(startIdx, endIdx);

                        console.log(
                            `${resourceId} | BATCH: ${batchIndex + 1}/${totalBatches} | RECORDS: ${batchData.length}`
                        );

                        try {
                            // Transform the batch data to match the expected format
                            const transformedBatch = batchData.map((record) =>
                                transformCapacityRecord(record, orgId, orgName)
                            );

                            // Call the database function to insert/update capacity data
                            const dbResponse = await this.db.query(
                                'SELECT * FROM public.tms_ace_bulk_upsert_capacity_data($1, $2, $3, $4)',
                                [
                                    JSON.stringify(transformedBatch),
                                    orgId,
                                    this.ip_address,
                                    this.user_agent_,
                                ]
                            );

                            // Parse the database response
                            const result =
                                dbResponse[0]
                                    ?.tms_ace_bulk_upsert_capacity_data;

                            if (result && result.status) {
                                results.push({
                                    batchIndex: batchIndex + 1,
                                    recordCount: batchData.length,
                                    success: true,
                                    message: result.message,
                                    data: result.data,
                                    statusCode: 200,
                                });
                                successCount +=
                                    result.data.success_count ||
                                    batchData.length;
                                failureCount += result.data.failure_count || 0;
                            } else {
                                results.push({
                                    batchIndex: batchIndex + 1,
                                    recordCount: batchData.length,
                                    success: false,
                                    message:
                                        result?.message ||
                                        'Database operation failed',
                                    statusCode: 500,
                                });
                                failureCount += batchData.length;
                            }
                        } catch (error) {
                            console.error(
                                `CapacityUpdateModel::processResourceCapacityUpdate:: Error processing batch ${batchIndex + 1} for resource ID ${resourceId}:`,
                                error
                            );
                            results.push({
                                batchIndex: batchIndex + 1,
                                recordCount: batchData.length,
                                success: false,
                                message: `Error processing batch: ${error.message || 'Unknown error'}`,
                                statusCode: error.statusCode || 500,
                            });
                            failureCount += batchData.length;
                        }
                    }

                    // Return the results
                    const isFullySuccessful = failureCount === 0;
                    resolve({
                        status: isFullySuccessful,
                        message: isFullySuccessful
                            ? `Capacity update for resource ID ${resourceId} completed successfully using database operations`
                            : `Capacity update for resource ID ${resourceId} completed with ${failureCount} failures out of ${capacityData.length} records using database operations`,
                        data: {
                            resourceId,
                            totalRecords: capacityData.length,
                            successCount,
                            failureCount,
                            processingMethod: 'database_function',
                            sampleResults: results.slice(0, 3), // Include just a few sample results
                        },
                        timestamp: new Date().toISOString(),
                    });
                } catch (dbError) {
                    console.error(
                        `CapacityUpdateModel::processResourceCapacityUpdate:: Error fetching capacity data for resource ID ${resourceId}:`,
                        dbError
                    );
                    resolve({
                        status: false,
                        message:
                            dbError.message ||
                            `Failed to fetch capacity data for resource ID ${resourceId}`,
                        timestamp: new Date().toISOString(),
                    });
                }
            } catch (error) {
                console.error(
                    `CapacityUpdateModel::processResourceCapacityUpdate:: Error in processResourceCapacityUpdate for resource ID ${resourceId}:`,
                    error
                );
                resolve({
                    status: false,
                    message:
                        error.message ||
                        `Failed to process capacity update for resource ID ${resourceId}`,
                    timestamp: new Date().toISOString(),
                });
            }
        });
    }

    // Getter and setter methods
    set ip_addr(ip_addr_) {
        this.ip_address = ip_addr_;
    }

    get ip_addr() {
        return this.ip_address;
    }

    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    get user_agent() {
        return this.user_agent_;
    }

    /**
     * Updates capacity data at the bookings service
     * This function is called from both direct query and stream processing blocks
     * to update capacity information in the bookings service
     *
     * @param {Array} capacityData - The capacity data fetched from the database
     * @param {string} orgId - The organization ID
     * @param {string} orgName - The organization name
     * @returns {Object} - Status object with success/failure information
     */
    async updateCapacityAtBookingsService(capacityData, orgId, orgName) {
        // Call the test function to verify Lambda's capability to handle 50k records
        // Comment this line out in production after testing
        // await this.testLambdaWith50kRecords(orgId, orgName);
        // return;
        console.log(
            `Starting capacity update process for organization: ${orgName} (ID: ${orgId}) - Processing ${capacityData.length} records`
        );

        try {
            // Check if there's an organization-specific lambda ARN in environment variables
            // Format: CAPACITY_LAMBDA_ARN_ORG_{orgId}
            const orgSpecificLambdaArnKey = `CAPACITY_LAMBDA_ARN_ORG_${orgId}`;
            let lambdaARN = process.env[orgSpecificLambdaArnKey];

            // If no org-specific lambda ARN found, use the generic one
            if (!lambdaARN) {
                lambdaARN = process.env.CAPACITY_LAMBDA_ARN_GENERIC;
            }

            // If no lambda ARN found, use the default bookings service lambda
            if (!lambdaARN) {
                // Use the generic AWS Lambda ARN for the bookings service
                lambdaARN =
                    process.env.TMS_SERVICES_BOOKINGS_LAMBDA ||
                    'tms-services-bookings-m-dev';
            }

            // Process capacity data in larger batches for better performance
            const BATCH_SIZE = 3000; // Increased batch size as requested
            const totalBatches = Math.ceil(capacityData.length / BATCH_SIZE);
            const results = [];
            let successCount = 0;
            let failureCount = 0;

            // Process capacity data in batches by adding each batch to a dedicated queue
            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const startIdx = batchIndex * BATCH_SIZE;
                const endIdx = Math.min(
                    startIdx + BATCH_SIZE,
                    capacityData.length
                );
                const batchData = capacityData.slice(startIdx, endIdx);

                console.log(
                    `Adding capacity batch ${batchIndex + 1} of ${totalBatches} with ${batchData.length} records to processing queue`
                );

                try {
                    // Transform the batch data before adding to queue to save Redis storage
                    const transformedBatch = batchData.map((record) =>
                        transformCapacityRecord(record, orgId, orgName)
                    );

                    // Prepare job data for the queue with transformed data
                    const jobData = {
                        transformedBatch, // Store transformed data instead of raw data
                        orgId,
                        orgName,
                        lambdaARN,
                        batchIndex,
                        totalBatches,
                    };

                    // Add job to the dedicated batch update queue with retry configuration
                    allQueues.CRON_CAPACITY_BATCH_UPDATE.addJob(jobData, {
                        attempts: 10, // Retry up to 10 times
                        backoff: {
                            type: 'exponential',
                            delay: 5000, // Start with 5 seconds delay, then exponential backoff
                        },
                        removeOnComplete: true,
                        removeOnFail: false, // Keep failed jobs for inspection
                    });

                    // Track the batch as a pending result
                    results.push({
                        batchIndex: batchIndex + 1,
                        recordCount: batchData.length,
                        success: true,
                        message: 'Batch added to processing queue',
                        statusCode: 202, // Accepted
                        queued: true,
                    });

                    // Count as success for now (actual processing happens asynchronously)
                    successCount += batchData.length;
                } catch (queueError) {
                    console.error(
                        `Error adding batch ${batchIndex + 1} to queue:`,
                        queueError,
                        '\nBatch data sample:',
                        batchData.slice(0, 2) // Log just a sample of the batch data
                    );

                    // Get stack trace for better debugging
                    const stackTrace = queueError.stack || '';

                    // Add batch error to results
                    results.push({
                        batchIndex: batchIndex + 1,
                        recordCount: batchData.length,
                        success: false,
                        message: `Error adding batch to queue: ${queueError.message || 'Unknown error'}`,
                        errorDetails: {
                            error: queueError.message || 'Unknown error',
                            code: queueError.code || 'QUEUE_ERROR',
                            stack: stackTrace
                                .split('\n')
                                .slice(0, 3)
                                .join('\n'), // Include first 3 lines of stack trace
                            batchSize: batchData.length,
                        },
                        statusCode: 500,
                    });

                    failureCount += batchData.length;
                }
            }

            // Return the results
            const isFullySuccessful = failureCount === 0;

            // Get error samples for reporting
            const errorSamples = results.filter((r) => !r.success).slice(0, 5);

            // Count different types of errors for better diagnostics
            const errorTypes = {};
            results
                .filter(
                    (r) => !r.success && r.errorDetails && r.errorDetails.code
                )
                .forEach((r) => {
                    const code = r.errorDetails.code;
                    errorTypes[code] = (errorTypes[code] || 0) + 1;
                });

            return {
                status: isFullySuccessful,
                message: isFullySuccessful
                    ? 'Capacity update at bookings service completed successfully'
                    : `Capacity update completed with ${failureCount} failures out of ${capacityData.length} records`,
                data: {
                    totalRecords: capacityData.length,
                    successCount,
                    failureCount,
                    errorSummary:
                        Object.keys(errorTypes).length > 0 ? errorTypes : null,
                    sampleResults: results.slice(0, 5), // Include just a few sample results
                    errorSamples: errorSamples.length > 0 ? errorSamples : null,
                },
                timestamp: new Date().toISOString(),
            };
        } catch (error) {
            console.error(
                'Error updating capacity at bookings service:',
                error
            );
            return {
                status: false,
                message:
                    error.message ||
                    'Failed to update capacity at bookings service',
                timestamp: new Date().toISOString(),
            };
        }
    }

    // transformCapacityRecord method has been moved to utils.js

    /**
     * Test function to verify Lambda's capability to handle 50k records
     * This simulates a real-world scenario with a large number of records
     * @param {string} orgId - The organization ID
     * @param {string} orgName - The organization name
     * @returns {Promise<Object>} - Status object with success/failure information
     */
    async testLambdaWith50kRecords(orgId, orgName) {
        console.log(
            `Starting capacity performance test with 50,000 records for organization: ${orgName} (ID: ${orgId})`
        );

        try {
            // Generate 50k test records
            const testRecords = generateTestCapacityRecords(3000);
            console.log(`Generated ${testRecords.length} test records`);

            // Check if there's an organization-specific lambda ARN in environment variables
            const orgSpecificLambdaArnKey = `CAPACITY_LAMBDA_ARN_ORG_${orgId}`;
            let lambdaARN = process.env[orgSpecificLambdaArnKey];

            // If no org-specific lambda ARN found, use the generic one
            if (!lambdaARN) {
                lambdaARN = process.env.CAPACITY_LAMBDA_ARN_GENERIC;
            }

            // If no lambda ARN found, use the default bookings service lambda
            if (!lambdaARN) {
                lambdaARN =
                    process.env.TMS_SERVICES_BOOKINGS_LAMBDA ||
                    'tms-services-bookings-m-dev';
            }

            // Process test records in batches
            const BATCH_SIZE = 5000;
            const totalBatches = Math.ceil(testRecords.length / BATCH_SIZE);
            let successCount = 0;
            let failureCount = 0;
            const startTime = new Date();

            console.log(
                `Processing ${totalBatches} batches of ${BATCH_SIZE} records each`
            );

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const batchStartTime = new Date();
                const startIdx = batchIndex * BATCH_SIZE;
                const endIdx = Math.min(
                    startIdx + BATCH_SIZE,
                    testRecords.length
                );
                const batchData = testRecords.slice(startIdx, endIdx);

                console.log(
                    `Adding test capacity batch ${batchIndex + 1} of ${totalBatches} with ${batchData.length} records to processing queue`
                );

                try {
                    // Transform the batch data before adding to queue to save Redis storage
                    const transformedBatch = batchData.map((record) =>
                        transformCapacityRecord(record, orgId, orgName)
                    );

                    // Prepare job data for the queue with transformed data
                    const jobData = {
                        transformedBatch, // Store transformed data instead of raw data
                        orgId,
                        orgName,
                        lambdaARN,
                        batchIndex,
                        totalBatches,
                        isTestBatch: true,
                    };

                    // Add job to the dedicated batch update queue with retry configuration
                    allQueues.CRON_CAPACITY_BATCH_UPDATE.addJob(jobData, {
                        attempts: 10, // Retry up to 10 times
                        backoff: {
                            type: 'exponential',
                            delay: 5000, // Start with 5 seconds delay, then exponential backoff
                        },
                        removeOnComplete: true,
                        removeOnFail: false, // Keep failed jobs for inspection
                    });

                    // Count as success for now (actual processing happens asynchronously)
                    successCount += batchData.length;

                    const batchEndTime = new Date();
                    const batchDuration =
                        (batchEndTime - batchStartTime) / 1000;

                    console.log(
                        `Test capacity batch ${batchIndex + 1} successfully added to processing queue in ${batchDuration.toFixed(2)} seconds`
                    );
                } catch (queueError) {
                    failureCount += batchData.length;
                    console.error(
                        `Error occurred while adding test capacity batch ${batchIndex + 1} to processing queue:`,
                        queueError.message
                    );
                }
            }

            const endTime = new Date();
            const totalDuration = (endTime - startTime) / 1000;
            const recordsPerSecond = Math.round(
                testRecords.length / totalDuration
            );

            console.log(
                `Capacity test completed in ${totalDuration.toFixed(2)} seconds`
            );
            console.log(
                `Results: ${successCount} records processed successfully, ${failureCount} records failed`
            );
            console.log(
                `Performance metrics: Processing rate of ${recordsPerSecond} records per second`
            );

            return {
                status: failureCount === 0,
                message: `Test with 50k records completed in ${totalDuration.toFixed(2)}s`,
                data: {
                    totalRecords: testRecords.length,
                    successCount,
                    failureCount,
                    durationSeconds: totalDuration,
                    recordsPerSecond,
                },
                timestamp: new Date().toISOString(),
            };
        } catch (error) {
            console.error('Error in 50k records test:', error);
            return {
                status: false,
                message: `Test with 50k records failed: ${error.message}`,
                timestamp: new Date().toISOString(),
            };
        }
    }

    // generateTestCapacityRecords method has been moved to utils.js

    /**
     * Process a single batch of capacity data and send it to the bookings lambda
     * This method is called by the queue processor
     *
     * @param {Object} transformedBatch - The transformed batch data to process
     * @param {string} orgId - The organization ID
     * @param {string} orgName - The organization name
     * @param {string} lambdaARN - The Lambda ARN to call
     * @param {number} batchIndex - The batch index
     * @param {number} totalBatches - The total number of batches
     * @returns {Promise<Object>} - Result of the batch processing
     */
    async processSingleCapacityBatch(
        transformedBatch,
        orgId,
        orgName,
        lambdaARN,
        batchIndex,
        totalBatches
    ) {
        // Validate input parameters
        if (!transformedBatch || !transformedBatch.length) {
            throw {
                batchIndex: batchIndex + 1,
                success: false,
                message: 'No transformed batch data provided',
                retryable: false, // Don't retry for invalid input
            };
        }

        console.log(
            `Preparing to process capacity batch ${batchIndex + 1}/${totalBatches} containing ${transformedBatch.length} records for organization: ${orgName} (ID: ${orgId})`
        );

        try {
            // Call the batch capacity API endpoint with the transformed batch
            const lambdaResponse = await callLambdaWithBatchData(
                transformedBatch,
                orgId,
                orgName,
                lambdaARN
            );

            // Parse the response
            const responseData = parseLambdaResponse(lambdaResponse);

            // Evaluate the response success/failure
            const result = evaluateResponseSuccess(
                responseData,
                lambdaResponse.StatusCode
            );

            if (result.success) {
                return createSuccessResponse(
                    result,
                    responseData,
                    transformedBatch,
                    batchIndex
                );
            } else {
                throw createErrorResponse(result, transformedBatch, batchIndex);
            }
        } catch (error) {
            // If it's already our formatted error, just rethrow it
            if (error.batchIndex && error.success === false) {
                throw error;
            }

            // Otherwise, format the error properly
            console.error(
                `Error occurred while processing capacity batch ${batchIndex + 1}:`,
                error,
                '\nSample of affected data:',
                transformedBatch.slice(0, 2) // Log just a sample of the batch data
            );

            // Get stack trace for better debugging
            const stackTrace = error.stack || '';

            // Create and throw a formatted error
            throw {
                batchIndex: batchIndex + 1,
                recordCount: transformedBatch.length,
                success: false,
                message: `Capacity batch ${batchIndex + 1} error: ${error.message || 'Unknown batch processing error'}`,
                errorDetails: {
                    error: error.message || 'Unknown batch processing error',
                    code: error.code || 'BATCH_PROCESSING_ERROR',
                    stack: stackTrace.split('\n').slice(0, 3).join('\n'), // Include first 3 lines of stack trace
                    batchSize: transformedBatch.length,
                },
                statusCode: 500,
                retryable: true, // Indicate this error can be retried
            };
        }
    }

    // _callLambdaWithBatchData method has been moved to utils.js as callLambdaWithBatchData

    // _parseLambdaResponse method has been moved to utils.js as parseLambdaResponse

    // _evaluateResponseSuccess method has been moved to utils.js as evaluateResponseSuccess

    // _createSuccessResponse method has been moved to utils.js as createSuccessResponse

    // _createErrorResponse method has been moved to utils.js as createErrorResponse

    // Method to get a fresh instance of the model
    getFreshInstance(model) {
        const clonedInstance = new CapacityUpdateModel();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new CapacityUpdateModel();
