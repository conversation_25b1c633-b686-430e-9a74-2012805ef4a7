CREATE OR REPLACE FUNCTION public.tms_hlpr_get_srvc_types_config_data(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		srvc_type_ids_ int[];	
		resp_data_ json;
	BEGIN
		srvc_type_ids_ = array(SELECT json_array_elements_text(json_extract_path(form_data_,'srvc_type_ids')))::int[];
	
		select json_object_agg(
				service_types_.service_type_id, service_types_.form_data 
			   ) 
		  from cl_cf_service_types as service_types_
		 where service_types_.service_type_id = any(srvc_type_ids_)
		  into resp_data_;
		 
		if resp_data_ is null then
			resp_data_ = '{}';
		end if;
	
		return resp_data_;
	END;
$function$
;
