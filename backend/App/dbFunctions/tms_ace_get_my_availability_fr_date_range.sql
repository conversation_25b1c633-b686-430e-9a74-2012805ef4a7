CREATE OR REPLACE FUNCTION public.tms_ace_get_my_availability_fr_date_range(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare
	status boolean;
	message text;
	affected_rows integer;
	resp_data json;
	--form data
	org_id_ integer;
	usr_id_ uuid;
	ip_address_ text;
	user_agent_ text;
    start_date_ date;
    end_date_ date;

	availability_data json;
	org_timezone text;

    org_availability_slots_config json;

begin

	status = false;
	message = 'Internal_error';
	--form data
	usr_id_ = form_data->>'usr_id';
	ip_address_ = form_data->>'ip_address';
	user_agent_ = form_data->>'user_agent';
	org_id_ = (form_data->>'org_id')::integer;
	start_date_ = DATE(form_data->>'start_date');
	end_date_ = DATE(form_data->>'end_date');
		
	org_timezone = tms_hlpr_get_org_timezone(org_id_);

    select  org_settings.settings_data
      from  public.cl_tx_orgs_settings as org_settings
     where  org_settings.org_id = org_id_
       and  org_settings.settings_type = 'ACE_AVAILABILITY_SLOTS_CONFIG'
      into  org_availability_slots_config;

    if org_availability_slots_config is null then
        status = false;
        message = 'capacity_details_missing';
        resp_data = jsonb_build_object(
            'status', status,
            'code', message
        );
        return resp_data;
    end if;

    -- Generate availability data for each day in the date range
    availability_data := array_to_json(array(
        select jsonb_build_object(
            'date', current_day,
            'slots', (
                select array_to_json(array(
                    select jsonb_build_object(
                        'db_id', slot_->>'value',
                        'start_time', slot_->>'start',
                        'end_time', slot_->>'end',
                        'day', current_day,
                        'label', slot_->>'label',
                        'is_available', COALESCE(usr_availability.is_present, false)
                    )
                    from json_array_elements(org_availability_slots_config->'generated_slots') as slot_
                    left join cl_tx_usr_availability as usr_availability
                      on usr_availability.usr_tmzone_day = current_day
                     and usr_availability.user_id = usr_id_
                     and usr_availability.is_active is true
                     and tms_hlpr_match_usr_availability(usr_availability, slot_, org_timezone, current_day) is true
                ))
            )
        )
         from (
        select generate_series(start_date_, end_date_, '1 day'::interval)::date as current_day
    ) as dates
    ));

    status = true;
    message = 'success';
    resp_data := jsonb_build_object(
        'status', status,
        'code', message,
        'data', jsonb_build_object(
            'availability', availability_data
           
        )
    );

    return resp_data;

end;
$function$
;
