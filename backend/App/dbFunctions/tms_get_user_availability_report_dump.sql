CREATE OR REPLACE FUNCTION public.tms_get_user_availability_report_dump(requester json, filters_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	status boolean;
	message text;
	affected_rows integer;
	resp_data json;
	--form data
	org_id_ integer;
	usr_id_ uuid;
	ip_address_ text;
	user_agent_ text;

	availability_data json;
	total_count integer;
	offset_val integer;
    slots json;
	org_timezone text;
    -- usr_capacity_details json;

    org_availability_slots_config json;
    filter_days text[];
    filter_from_date timestamp;
    filter_to_date timestamp;
    filter_available text[];
   	current_date_ timestamp;
    --filter_available text[];
    filter_locations text[];
    filter_technician_roles int[];
    filter_service_hubs int[];
    data_ json;
    pagination json; 
begin

	status = false;
	message = 'Internal_error';
	--form data 
	usr_id_ = requester->>'usr_id';
	ip_address_ = requester->>'ip_address';  
	user_agent_ = requester->>'user_agent';
	org_id_ = (requester->>'org_id')::integer;

	-- Calculate offset
	

    org_timezone = tms_hlpr_get_org_timezone(org_id_);
    current_date_ = (now() at time zone 'utc');
    
    filter_days = array(select json_array_elements_text(json_extract_path(filters_,'days')))::text[];
	if cardinality(filter_days) > 0 then
	 	filter_from_date =  filter_days[1]::timestamp;
		filter_to_date   =  filter_days[2]::timestamp;
	else
		filter_from_date =  current_date_;
		filter_to_date   =  current_date_;
	end if;

    filter_available = array(select json_array_elements_text(json_extract_path(filters_,'availability')))::text[];
   --  filter_available = array(select json_array_elements_text(json_extract_path(filters_,'availability')))::text[];
    filter_locations = array(select json_array_elements_text(json_extract_path(filters_,'locations')))::text[];
    filter_technician_roles = array(select json_array_elements_text(json_extract_path(filters_,'technician_roles')))::int[];
    filter_service_hubs = array(select json_array_elements_text(json_extract_path(filters_,'service_hubs')))::int[];

    select  org_settings.settings_data
      from  public.cl_tx_orgs_settings as org_settings
     where  org_settings.org_id = org_id_
       and  org_settings.settings_type = 'ACE_AVAILABILITY_SLOTS_CONFIG'
      into  org_availability_slots_config;

     if org_availability_slots_config is null then
        status = false;
        message = 'capacity_details_missing';
        resp_data = jsonb_build_object(
            'status', status,
            'code', message
        );
        return resp_data;
        --raise exception 'capacity_details_missing';
     end if;


     resp_data =  array_to_json(array(
                    select json_build_object(                       
	                    'Technician Name',user_."name",
	                    'Available', case 
		                                 when usr_availability.is_present is true then 'Yes'
		                                 else 'No'
	                                 end,
                        'Day',date(day_),
	                    'Time Slots',slot_->>'label',
	                    'Updated On',(usr_availability.u_meta).time  
                    )
                    from cl_tx_users user_
				   inner join generate_series(filter_from_date, filter_to_date, '1 day'::interval) as day_
					  on true
				   inner join json_array_elements(org_availability_slots_config->'generated_slots') as slot_
					  on true
					left join cl_tx_usr_availability usr_availability 
					  on usr_availability.user_id  = user_.usr_id
					 and usr_availability.usr_tmzone_day = date(day_)
					 and usr_availability.is_active is true
					 and tms_hlpr_match_usr_availability(usr_availability,slot_,org_timezone,usr_tmzone_day ) is true  
				   inner join cl_tx_usr_roles as usr_role
				      on usr_role.user_id = user_.usr_id 
				     and (
				   			cardinality(filter_technician_roles) = 0 
				   		 	or usr_role.role_id = any(filter_technician_roles)
				   	     )					
					where user_.primary_srvc_hub is not null
					  and user_.org_id = org_id_
					  and user_.is_active is true
					  and (
						    cardinality(filter_locations) = 0 
						    or exists (
						        select 1
						          from unnest(filter_locations::int[]) as filter_location(location_id)
		       				     where location_id = any(user_.loc_group::int[]) 
						    )
						   )
					   and (
					         cardinality(filter_service_hubs) = 0 
					         or user_.primary_srvc_hub = any(filter_service_hubs)
					       )		
				      order by  user_."name" ,day_,(slot_->>'start')::time                           
					
	  ));

--    raise notice 'data_ %',data_;
   

	return resp_data;

end;
$function$
;
