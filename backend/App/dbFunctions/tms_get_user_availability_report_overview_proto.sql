CREATE OR REPLACE FUNCTION public.tms_get_user_availability_report_overview_proto(form_data json, filters_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	status boolean;
	message text;
	affected_rows integer;
	resp_data json;
	--form data
	org_id_ integer;
	usr_id_ uuid;
	ip_address_ text;
	user_agent_ text;
	filters_ json;

	total_capacity integer;
	available_capacity integer;
	utilized_capacity integer;
	utilization_percentage numeric;
    role_data json;
    filter_proto json;
   
   -- Overview counts
	total_users integer;
	present_users integer;
	limited_users integer;
	offline_users integer;
   	-- Filter variables
	filter_days text[];
	filter_from_date timestamp;
	filter_to_date timestamp;
	filter_locations json;
	filter_technician_roles int[];
	filter_service_hubs int[];
	current_date_ timestamp;
	org_timezone text;
	org_availability_slots_config json;
    service_hubs json;

begin

	status = false;
	message = 'Internal_error';
	--form data 
	usr_id_ = form_data->>'usr_id';
	ip_address_ = form_data->>'ip_address';  
	user_agent_ = form_data->>'user_agent';
	org_id_ = (form_data->>'org_id')::integer;
	filters_ = form_data->'filters';

    -- Parse filters
	filter_days = array(select json_array_elements_text(json_extract_path(filters_,'days')))::text[];
	if cardinality(filter_days) > 0 then
		filter_from_date = filter_days[1]::timestamp;
		filter_to_date = filter_days[2]::timestamp;
	else
		filter_from_date = current_date_;
		filter_to_date = current_date_;
	end if;

	-- Calculate capacity metrics

   	-- Get organization availability slots configuration
	select org_settings.settings_data
	from public.cl_tx_orgs_settings as org_settings
	where org_settings.org_id = org_id_
	and org_settings.settings_type = 'ACE_AVAILABILITY_SLOTS_CONFIG'
	into org_availability_slots_config;

    if org_availability_slots_config is null then
		status = false;
		message = 'capacity_details_missing';
		resp_data = jsonb_build_object(
			'status', status,
			'code', message
		);
		return resp_data;
	end if;

	-- Calculate overview statistics
	
    role_data= array_to_json(array(
	   select jsonb_build_object(
					'value', (roles.role_id::text) ,
					'label', (roles.name_details).title ,
					'title', (roles.name_details).title
			   )
		 from public.cl_cf_roles as roles
		where roles.org_id = org_id_
		group by roles.role_id
		order by (roles.name_details).title
	));
  
    service_hubs = array_to_json(array(
	   select jsonb_build_object(
					'value', hubs.id  ,
					'label', hubs.hub_name  ,
					'title', hubs.hub_name 
			   )
		 from cl_tx_vertical_srvc_hubs as hubs
		where hubs.org_id = org_id_
		order by hubs.hub_name asc
	));

    filter_locations = tms_get_loc_grps_fr_select(form_data)->'data';
    
    filter_proto = json_build_object(
    					'technician_roles', role_data, 
						'service_hubs',service_hubs,
						'locations',filter_locations
   				   );
		
	status = true;
	message = 'success';
	resp_data := jsonb_build_object(
		'status', status,
		'code', message,
		'data', jsonb_build_object(			
			'filters_proto',filter_proto
			
		)
	);

	return resp_data;

end;
$function$
;
