CREATE OR REPLACE FUNCTION public.tms_get_srvc_hubs_by_search(form_data json, search_query text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	-- Declarations
	declare 
		status boolean;
	 	message text;
	 	resp_data json;
	 	org_id_ integer;

	    filter_search_query text;
	 
	begin
		status = false;
		message = 'Internal_error';
		resp_data = '{}'::json;
	
		--form data 
		org_id_      = json_extract_path_text(form_data,'org_id');		
	
	    if search_query !='' then
		filter_search_query = concat('%',search_query,'%');
	    end if;
	
		resp_data = array_to_json(array(
				    	select jsonb_build_object(	
				    	'id',srvc_hub.id,
						'value',srvc_hub.hub_name ,
						'label',srvc_hub.hub_name ,
						'key',srvc_hub.hub_name
					   )
				  from cl_tx_vertical_srvc_hubs srvc_hub
				 where srvc_hub.hub_name  ilike filter_search_query			   					  	      
				   and srvc_hub.org_id = org_id_
				 limit 10 
		));	
	
	   	status = true;
		message = 'success';
	
	   	return json_build_object('status',status,'code',message,'data',resp_data);
	
	end;
$function$
;
