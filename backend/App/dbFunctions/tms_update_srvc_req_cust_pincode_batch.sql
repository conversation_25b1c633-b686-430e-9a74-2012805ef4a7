DROP FUNCTION IF EXISTS public.tms_update_srvc_req_cust_pincode_batch(integer, integer, integer);

CREATE OR REPLACE FUNCTION public.tms_update_srvc_req_cust_pincode_batch(
    p_service_type_id integer,
    p_batch_size integer,
    p_offset integer
)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    status boolean;
    message text;
    resp_data json;
    updated_count integer;
    updated_ids bigint[];

begin 
    status = false;
    message = 'Internal_error';

    -- Update service requests in batch using subquery approach
    -- PostgreSQL doesn't support LIMIT/OFFSET in UPDATE, so we use a subquery
    -- Set cust_pincode to NULL if form_data contains empty string, otherwise use the value
    UPDATE public.cl_tx_srvc_req
    SET cust_pincode = CASE
        WHEN form_data->>'cust_pincode' = '' THEN NULL
        ELSE form_data->>'cust_pincode'
    END
    WHERE db_id IN (
        SELECT db_id
        FROM public.cl_tx_srvc_req
        WHERE srvc_type_id = p_service_type_id
        AND is_deleted IS NOT TRUE
        AND cust_pincode IS NULL
        AND form_data->>'cust_pincode' IS NOT NULL
        ORDER BY db_id
        LIMIT p_batch_size OFFSET p_offset
    );

    -- Get the count of updated records
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    -- Build response data
    resp_data = json_build_object(
        'updated_count', updated_count,
        'service_type_id', p_service_type_id,
        'batch_size', p_batch_size,
        'offset', p_offset
    );

    status = true;
    message = 'success';

    -- Return the result
    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );

exception
    when others then
        status = false;
        message = 'Database error: ' || SQLERRM;
        
        return json_build_object(
            'status', status,
            'message', message,
            'data', json_build_object(
                'updated_count', 0,
                'service_type_id', p_service_type_id,
                'batch_size', p_batch_size,
                'offset', p_offset
            )
        );
end;
$function$;
