CREATE OR REPLACE FUNCTION public.tms_get_srvc_reqs_v2(requester_info json, page_no integer, page_size integer, filter_ json, search_query text, is_count_only boolean DEFAULT false)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
-- 	Bare minimums
	status boolean;
	message text;
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;
	retrieve_count boolean default false;
--  Specific
	srvc_type_id_ integer;	
-- 	Filters
	filter_statuses text[];
	filter_vertical_id int;
   	filter_verticals_list int[];
	
--  Temps
	matching_ids_json json;
	matching_ids bigint[];
	temp_id bigint;
   	field_list json;
   	matching_ids_json_for_full_count json;

-- 	Output
	pagination json;
	total integer;
	data_ json;
	type_id_vs_config json;

    filter_search_query text;
    location_groups json;

begin 
	status = false;
	message = 'Internal_error';

	org_id_ = json_extract_path_text(requester_info,'org_id');
	usr_id_ = json_extract_path_text(requester_info,'usr_id');
	ip_address_ = json_extract_path_text(requester_info,'ip_address');
	user_agent_ = json_extract_path_text(requester_info,'user_agent');
	srvc_type_id_ = json_extract_path_text(requester_info,'srvc_type_id');
	retrieve_count = requester_info->>'retrieve_count';

	if retrieve_count is true then
		status = true;
    	message = 'success';
		data_ = tms_get_srvc_reqs_count(requester_info, page_no, page_size, filter_, search_query)->'data';
		return json_build_object('status',status,'code',message,'data',data_);
	end if;

	if search_query !='' then
		filter_search_query = concat('%',search_query,'%');								    			
	end if;

	if filter_->'verticals_list' is not null then
    	filter_verticals_list = (array(select (filter_->'verticals_list'->>0)));
		filter_vertical_id = filter_verticals_list[1];
    end if;

	filter_statuses = array( select json_array_elements_text(json_extract_path(filter_,'statuses')) )::text[];

	matching_ids_json = (select tms_get_srvc_reqs_by_filter_as_array_v2(requester_info, page_no, page_size, filter_, search_query, false));

    if is_count_only is true then--matching_ids_json->0->>'full_count';
    end if;

	FOR single_id_index IN 0..json_array_length(matching_ids_json) - 1 loop
	  temp_id = json_extract_path_text(matching_ids_json -> single_id_index,'id');
	 	
      matching_ids = array_append(matching_ids, temp_id);
    END LOOP;

  	if srvc_type_id_ = '0' then
  		select sp_custom_fields.settings_data->>'srvc_type_tabular_view_columns'     
	      from cl_tx_orgs_settings as sp_custom_fields  
	     where sp_custom_fields.db_id = filter_vertical_id
		  into field_list;
  	else
	  	 select srvc_type.form_data->>'srvc_type_tabular_view_columns'
		   from cl_cf_service_types as srvc_type 
	      where srvc_type.service_type_id = srvc_type_id_
		   into field_list;
  	end if;
  
    location_groups = array_to_json(array(
		select jsonb_build_object(
				'id', loc_grp.id ,
				'title', loc_grp.groups_name 
    		   ) 
	      from public.cl_tx_location_groups as loc_grp 
	     group by loc_grp.id 
	)); 
  
	data_ = array_to_json(array(
    	select jsonb_build_object(
    				'id', srvc_req.db_id ,
    				'time', (srvc_req.c_meta).time ,
    				'title', srvc_req.display_code ,
    				'status', srvc_req.status,
    				'priority', srvc_req.priority,
    				'srvc_type_id', srvc_req.srvc_type_id,
					'is_deleted',srvc_req.is_deleted,
    				'status', jsonb_build_object(
						'status_type', srvc_status.status_type,
						'title',srvc_status.title,
						'key', srvc_status.status_key,
						'color', srvc_status.color,
						'transition_date', srvc_trnstn_log.trnstn_date  
					),
    				'labels', jsonb_extract_path(srvc_req.form_data,'request_labels'),
    				'description',jsonb_extract_path(srvc_req.form_data,'request_description'),
    				'c_by_name', c_by_usr."name",
    				'c_time', (srvc_req.c_meta)."time" ,
    				'cust_name',jsonb_extract_path(srvc_req.form_data,'cust_full_name'),
    				'cust_city',jsonb_extract_path(srvc_req.form_data,'cust_city'),
    				'req_date',jsonb_extract_path(srvc_req.form_data,'request_req_date'),
    				'sbtsks_meta', jsonb_agg(
    					jsonb_build_object(
    						'sbtsk_id',sbtsk.db_id,
    						'sbtsk_type_title', sbtsk_type.title,
    						'sbtsk_type_icon', sbtsk_type.icon_code,
    						'sbtsk_status', sbtsk_status.title,
    						'sbtsk_status_color', sbtsk_status.color,
								'gai_rating', sbtsk.form_data->'gai_rating'
    					)
    					
    				) filter ( where sbtsk.db_id is not null ),
    				
    				'feedback_data', (srvc_req.form_data->'feedback_data')::jsonb ,
    				'Attachment_count', ( select sum(jsonb_array_length(value)) 
    							       from jsonb_each(srvc_req.form_data->'attachments')
    							   ),
    				'srvc_prvdr', srvc_req.form_data->>'new_prvdr',
    				'org', jsonb_build_object(
    					'value',org.org_id ,
	    				'icon_path',org.icon_path ,
	    				'label',org.nickname 
    				),
    				'addr', tms_extract_address(srvc_req.form_data,'cust_'),
    				'ext_order_id',srvc_req.form_data->>'79a88c7b-c64f-46c4-a277-bc80efa1c154',
    				'fields_data_for_table', tms_hlpr_get_fields_data_from_json(field_list, srvc_req.form_data),
    				'brand_location_groups', tms_hlpr_get_location_groups_name(location_groups, srvc_req.matching_brand_loc_grp_ids),
    				'prvdr_location_groups', tms_hlpr_get_location_groups_name(location_groups, srvc_req.matching_srvc_prvdr_loc_grp_ids),
    				'req_start_time', srvc_req.form_data->>'start_time',
    				'req_end_time', srvc_req.form_data->>'end_time',
    				'geocoding_location_data',srvc_req.form_data->'geocoding_location_data',
					'cust_pincode', srvc_req.form_data->>'cust_pincode',
					'is_restricted_view', srvc_req.is_restricted
    		   ) 
    		   
	      from public.cl_tx_srvc_req as srvc_req 
	     inner join cl_tx_orgs as org
	        on srvc_req.org_id = org.org_id 
	     inner join cl_cf_srvc_statuses as srvc_status
	        on srvc_status.srvc_id = srvc_req.srvc_type_id 
	       and srvc_status.status_key = srvc_req.status 
	     inner join cl_tx_users c_by_usr 
	        on c_by_usr.usr_id = srvc_req.c_by
	  
	      left join cl_tx_sbtsk as sbtsk
	        on sbtsk.srvc_req_id = srvc_req.db_id
 		   and sbtsk.is_deleted is not true
	       and case
            	   when tms_hlpr_is_org_srvc_prvdr_by_srvc_req_and_org_id(org_id_,sbtsk.srvc_req_id) is true  THEN sbtsk.org_id = org_id_
            	   else true 
     		   end 
	      left join cl_cf_sbtsk_types as sbtsk_type 
	        on sbtsk_type.sbtsk_type_id = sbtsk.sbtsk_type 
	      left join cl_cf_sbtsk_statuses sbtsk_status
	        on sbtsk_status.sbtsk_type_id = sbtsk.sbtsk_type 
	       and sbtsk_status.status_key = sbtsk.status
	     inner join cl_tx_srvc_req_trnstn_log as srvc_trnstn_log
	        on srvc_trnstn_log.srvc_req_id = srvc_req.db_id 
	       and srvc_trnstn_log.status_key = srvc_req.status
		 where srvc_req.db_id = any( matching_ids )
		 group by org.org_id,c_by_usr."name",srvc_status.db_id ,srvc_req.db_id , srvc_trnstn_log.trnstn_date 
		 order by array_position(matching_ids, srvc_req.db_id::bigint)
--		 order by srvc_req.db_id desc 
		 
	));
	
	if srvc_type_id_ = 0 then
		type_id_vs_config = array_to_json(array(
	    	select jsonb_build_object(
	    				'srvc_id',srvc_type.service_type_id,
						'srvc_code',srvc_type.service_code,
						'srvc_title',srvc_type.title,
						'srvc_desc',srvc_type.description,
						'srvc_icon',srvc_type.icon_code,
						'srvc_config',srvc_type.form_data
	    		   ) 
		      from public.cl_tx_srvc_req as srvc_req 
		     inner join cl_cf_service_types as srvc_type
		        on srvc_type.service_type_id = srvc_req.srvc_type_id 
			 where srvc_req.db_id = any( matching_ids )
			 group by srvc_type.service_type_id 
		));
	else 
		type_id_vs_config = '[]'::json;
	end if;
		    
--    pagination = jsonb_build_object('total', total  ,'current', page_no);
    resp_data =  jsonb_build_object('data',data_,'type_id_vs_config',type_id_vs_config);
   
--	    Sending the below by default
    status = true;
    message = 'success';
   
	return json_build_object('status',status,'code',message,'data',resp_data);
end ;
$function$
;
