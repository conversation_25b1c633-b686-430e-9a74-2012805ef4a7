CREATE OR REPLACE FUNCTION public.tms_wrkflw_srvc_req(form_data_ json, _config_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$

-- Declarations
declare 
-- 	Bare minimums
	status boolean;
	message text;
   
	srvc_type_id_ int;
	usr_org_id int;
	is_cust_access int;
	is_cust_req int;
	ins_id int;
	default_srvc_prvdr int;
	possible_srvc_prvdr int[];
	user_context_json json;
	initial_status_update_json json;
	default_srvc_prvdr_included_in_possible_srvc_prvdr bool;
	defined_srvc_prvdr_included_in_possible_srvc_prvdr bool;
	assigned_srvc_prvdr_json json;
	resp_data json;
 	srvc_prvdr_id_ int;
BEGIN
	status = false;
	message = 'internal_error';
	
	srvc_type_id_ 	= form_data_->>'srvc_type_id';
	usr_org_id    	= form_data_->>'usr_org_id';
	is_cust_access  = form_data_->>'is_customer_access';
	is_cust_req  = form_data_->>'is_cust_req';
	ins_id 			= form_data_->>'ins_id';

	--Get user_context_json
    user_context_json = tms_get_user_context_from_data(form_data_);
    user_context_json = jsonb_set(user_context_json::jsonb,'{srvc_type_id}',to_jsonb(form_data_->>'srvc_type_id'),true);
	srvc_prvdr_id_ = form_data_->>'service_provider_id';
	--Assign service provider start
	--Check
    --1st srvc_enable_srvc_prvdr is should be enable.
    --2nd default srvc_prvdr should be in possible_srvc_prvdr.
    --3nd srvc_default_provider is should be assigned.
    --4rd if is_cust_access = 1 then new_prvdr set usr_org_id
    
	--Get possible_srvc_prvdr by srvc_type_id
	select array(SELECT json_array_elements_text(json_extract_path(possible_srvc_prvdr.form_data,'srvc_possible_prvdrs')))
	  from cl_cf_service_types as  possible_srvc_prvdr
	 where possible_srvc_prvdr.service_type_id = srvc_type_id_
	  into possible_srvc_prvdr;
--	 raise notice 'possible_srvc_prvdr %', possible_srvc_prvdr; 
	 
	--Get default_srvc_prvdr from config
	if _config_data->>'srvc_default_provider' != '' and srvc_prvdr_id_ is null and is_cust_req is null then
		default_srvc_prvdr = _config_data->>'srvc_default_provider';
		default_srvc_prvdr_included_in_possible_srvc_prvdr = (select default_srvc_prvdr = any(possible_srvc_prvdr));
		if (
		  		( 
		  			(_config_data->>'srvc_enable_srvc_prvdr')::bool is true and 
		  			 default_srvc_prvdr_included_in_possible_srvc_prvdr is true and
		  			 (_config_data->>'srvc_default_provider' is not null and _config_data->>'srvc_default_provider' <> '' )
		  		) 
		  		or 
		  		is_cust_access = '1'
		   ) then 
		   
		   	  if is_cust_access = '1' then 
		   	  	assigned_srvc_prvdr_json = jsonb_set(user_context_json::jsonb,'{new_prvdr}',to_jsonb(usr_org_id),true);
		   	  else
		   	  	assigned_srvc_prvdr_json = jsonb_set(user_context_json::jsonb,'{new_prvdr}',to_jsonb(_config_data->>'srvc_default_provider'),true);
			  end if;
			 
			  --PERFORM tms_create_service_request(assigned_srvc_prvdr_json,ins_id);
			  resp_data = tms_create_service_request(assigned_srvc_prvdr_json,ins_id);
				 
		end if;

	elsif (is_cust_access = '1' or is_cust_req = '1') and srvc_prvdr_id_ is null then
		assigned_srvc_prvdr_json = jsonb_set(user_context_json::jsonb,'{new_prvdr}',to_jsonb(usr_org_id),true);
		resp_data = tms_create_service_request(assigned_srvc_prvdr_json,ins_id);
	end if;
	if srvc_prvdr_id_ is not null then
		defined_srvc_prvdr_included_in_possible_srvc_prvdr = (select srvc_prvdr_id_ = any(possible_srvc_prvdr));
		if defined_srvc_prvdr_included_in_possible_srvc_prvdr is true then
  			assigned_srvc_prvdr_json = jsonb_set(user_context_json::jsonb,'{new_prvdr}',to_jsonb(form_data_->>'service_provider_id'),true);
  			
  			--PERFORM tms_create_service_request(assigned_srvc_prvdr_json,ins_id);
		    resp_data = tms_create_service_request(assigned_srvc_prvdr_json,ins_id);
  		end if;
  	end if;

	--Assign service provider end
	
	return resp_data;
END;
$function$
;
