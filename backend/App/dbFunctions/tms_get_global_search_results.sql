CREATE OR REPLACE FUNCTION public.tms_get_global_search_results(requester_info json, search_query text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	-- 	Bare minimums
	status boolean;
	message text;
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;
	is_srvc_prvdr boolean;
	extra_where text;
	req_org_where text;
	dynamic_sql text;
	filter_search_query text;
	srvc_type_id_vs_authorities json;
  	usr_has_access_to_srvc_requests json;
  	usr_has_authority_fr_srvc_requests json;
  	filter_locations text[];
	feature_access_fr_TMS250414073641 boolean;
	is_org_srvc_prvdr_ boolean;

begin
	status = false;
	message = 'Internal_error';

	org_id_ = json_extract_path_text(requester_info,'org_id');
	usr_id_ = json_extract_path_text(requester_info,'usr_id');
	ip_address_ = json_extract_path_text(requester_info,'ip_address');
	user_agent_ = json_extract_path_text(requester_info,'user_agent');
	feature_access_fr_TMS250414073641 = tms_hlpr_get_feature_access_fr_current_usr(requester_info, 'TMS250414073641');
	is_org_srvc_prvdr_ = tms_hlpr_is_org_srvc_prvdr(org_id_);

	if search_query !='' then
		filter_search_query = concat('%',search_query,'%');
	else
		message = 'No search query entered.';
		return json_build_object('status',status,'code',message,'data',resp_data);
	end if;

	usr_has_access_to_srvc_requests = (tms_hlpr_usr_has_access_to_srvc_requests(usr_id_,org_id_)::json) ;
    usr_has_authority_fr_srvc_requests = (tms_hlpr_usr_has_authority_fr_srvc_requests(usr_id_,org_id_));
 	srvc_type_id_vs_authorities = tms_hlpr_get_srvc_type_id_vs_authorities(org_id_);
 	filter_locations = tms_get_assigned_loc_to_user(usr_id_);

	select array_to_json(array(  
		 select jsonb_build_object( 
				   		'id',srvc_req.db_id , 
						'display_code', srvc_req.display_code,
						'collab_order_id', srvc_req.collab_order_id,
						'srvc_type_id', srvc_req.srvc_type_id,
						'is_deleted', srvc_req.is_deleted
				   )
		   from cl_tx_srvc_req as srvc_req
		  where ( 
					srvc_req.display_code ilike filter_search_query
			   		or srvc_req.collab_order_id ilike filter_search_query
			  	)
			and	(
		  			srvc_req.org_id = org_id_
		  			or srvc_req.srvc_prvdr = org_id_ 
		  		)
			and tms_hlpr_check_usr_accesibility_to_srvc_req(
			  	  	srvc_req,
			  	  	srvc_type_id_vs_authorities,
			  	  	usr_has_access_to_srvc_requests,
			  	  	usr_has_authority_fr_srvc_requests,
			  	  	filter_locations,
					false,
			  	  	org_id_,
			  	  	usr_id_,
			  	  	is_srvc_prvdr
				)
			and (
					is_org_srvc_prvdr_ is not true
					or
					feature_access_fr_TMS250414073641 is not true
					or
					(
						feature_access_fr_TMS250414073641 is true
						and 
						is_org_srvc_prvdr_ is true
						and 
						srvc_req.is_restricted is not true
					)
				)
		  group by srvc_req.db_id
		  order by srvc_req.db_id desc
		  limit 5
		  into resp_data
	));
	
	status = true;
    message = 'success';
	return json_build_object('status',status,'code',message,'data',resp_data);
	
END;
$function$
;
