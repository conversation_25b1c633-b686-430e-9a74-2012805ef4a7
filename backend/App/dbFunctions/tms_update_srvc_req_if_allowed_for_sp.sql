CREATE OR REPLACE FUNCTION public.tms_update_srvc_req_if_allowed_for_sp(form_data_ json, srvc_type_id_ integer)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare 
		resp_data_ json;
		srvc_prvdr_restricted_request_status_ text[];
		affected_rows integer;
		status boolean;
		message text;
	begin
		affected_rows = 0;
		status = false;
		message = 'Internal_error';
	
		srvc_prvdr_restricted_request_status_ =  array(SELECT json_array_elements_text(json_extract_path(form_data_,'srvc_prvdr_restricted_request_status')));
	
		update cl_tx_srvc_req as srvc_req_
		   set is_restricted = srvc_req_.status = any(srvc_prvdr_restricted_request_status_)
		  from cl_cf_service_types as service_type_
		 where service_type_.service_type_id = srvc_type_id_ 
		   and srvc_req_.srvc_type_id = service_type_.service_type_id;
		   get diagnostics affected_rows = ROW_COUNT;
	
		if affected_rows > 0 then
			status = true;
			message = 'success';
			resp_data_ =  json_build_object('affected_rows',affected_rows);
		end if;
	
		return json_build_object('status',status,'code',message,'data',resp_data_);
	END;
$function$
;
