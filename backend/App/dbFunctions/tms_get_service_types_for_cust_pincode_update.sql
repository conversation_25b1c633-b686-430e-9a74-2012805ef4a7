DROP FUNCTION IF EXISTS public.tms_get_service_types_for_cust_pincode_update();

CREATE OR REPLACE FUNCTION public.tms_get_service_types_for_cust_pincode_update()
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    status boolean;
    message text;
    resp_data json;

begin 
    status = false;
    message = 'Internal_error';

    -- Get service types with counts of service requests that need cust_pincode updates
    -- Only include service types that have records needing updates
    resp_data = array_to_json(array(
           SELECT json_build_object(
                    'srvc_type_id', srvc_req.srvc_type_id,
                    'count', COUNT(srvc_req.db_id)
                  ) as service_type_data
             FROM cl_tx_srvc_req as srvc_req
            WHERE srvc_req.is_deleted IS NOT TRUE
              AND srvc_req.cust_pincode IS NULL
              AND srvc_req.form_data->>'cust_pincode' IS NOT NULL
            GROUP BY srvc_req.srvc_type_id
           HAVING COUNT(srvc_req.db_id) > 0
            ORDER BY srvc_req.srvc_type_id
    ));

    -- If no data found, return empty array
    IF resp_data IS NULL THEN
        resp_data = '[]'::json;
    END IF;

    status = true;
    message = 'success';

    -- Return the result
    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );

exception
    when others then
        status = false;
        message = 'Database error: ' || SQLERRM;
        
        return json_build_object(
            'status', status,
            'message', message,
            'data', '[]'::json
        );
end;
$function$;
